-- 移除接口日志表中的请求体和响应体字段
-- 这些数据将存储在MongoDB中

-- 备份现有数据（可选）
-- CREATE TABLE ext_api_log_backup AS SELECT * FROM ext_api_log;

-- 移除字段
ALTER TABLE ext_api_log DROP COLUMN IF EXISTS request_body;
ALTER TABLE ext_api_log DROP COLUMN IF EXISTS response_body;

-- 添加注释说明
ALTER TABLE ext_api_log COMMENT = '接口日志表 - 报文数据已迁移至MongoDB';

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_ext_api_log_config_id ON ext_api_log(config_id);
CREATE INDEX IF NOT EXISTS idx_ext_api_log_batch_no ON ext_api_log(batch_no);
CREATE INDEX IF NOT EXISTS idx_ext_api_log_request_time ON ext_api_log(request_time);
CREATE INDEX IF NOT EXISTS idx_ext_api_log_status ON ext_api_log(status);
