package com.yhl.scp.dcp.apiLog.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.dcp.apiLog.convertor.ExtApiLogConvertor;
import com.yhl.scp.dcp.apiLog.domain.entity.ExtApiLogDO;
import com.yhl.scp.dcp.apiLog.domain.service.ExtApiLogDomainService;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.infrastructure.dao.ExtApiLogDao;
import com.yhl.scp.dcp.apiLog.infrastructure.po.ExtApiLogPO;
import com.yhl.scp.dcp.apiLog.mq.ExtApiLogMessageProducer;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.apiLog.vo.ExtApiLogVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <code>ExtApiLogServiceImpl</code>
 * <p>
 * 接口日志表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-21 13:43:10
 */
@Slf4j
@Service
public class ExtApiLogServiceImpl extends AbstractService implements ExtApiLogService {

    @Resource
    private ExtApiLogDao extApiLogDao;

    @Resource
    private ExtApiLogDomainService extApiLogDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private ExtApiLogMessageProducer extApiLogMessageProducer;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(ExtApiLogDTO extApiLogDTO) {
        // 0.数据转换
        ExtApiLogDO extApiLogDO = ExtApiLogConvertor.INSTANCE.dto2Do(extApiLogDTO);
        ExtApiLogPO extApiLogPO = ExtApiLogConvertor.INSTANCE.dto2Po(extApiLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        extApiLogDomainService.validation(extApiLogDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(extApiLogPO);
        extApiLogDao.insert(extApiLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(ExtApiLogDTO extApiLogDTO) {
        // 0.数据转换
        ExtApiLogDO extApiLogDO = ExtApiLogConvertor.INSTANCE.dto2Do(extApiLogDTO);
        ExtApiLogPO extApiLogPO = ExtApiLogConvertor.INSTANCE.dto2Po(extApiLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        extApiLogDomainService.validation(extApiLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(extApiLogPO);
        extApiLogDao.update(extApiLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ExtApiLogDTO> list) {
        List<ExtApiLogPO> newList = ExtApiLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        extApiLogDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<ExtApiLogDTO> list) {
        List<ExtApiLogPO> newList = ExtApiLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        extApiLogDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return extApiLogDao.deleteBatch(idList);
        }
        return extApiLogDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ExtApiLogVO selectByPrimaryKey(String id) {
        ExtApiLogPO po = extApiLogDao.selectByPrimaryKey(id);
        return ExtApiLogConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "EXT_API_LOG")
    public List<ExtApiLogVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "EXT_API_LOG")
    public List<ExtApiLogVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ExtApiLogVO> dataList = extApiLogDao.selectByCondition(sortParam, queryCriteriaParam);
        ExtApiLogServiceImpl target = SpringBeanUtils.getBean(ExtApiLogServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ExtApiLogVO> selectByParams(Map<String, Object> params) {
        List<ExtApiLogPO> list = extApiLogDao.selectByParams(params);
        return ExtApiLogConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ExtApiLogVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }
    public BaseResponse<Void> update(ExtApiLogDTO extApiLogDTO) {
        // 0.数据转换
        ExtApiLogDO extApiLogDO = ExtApiLogConvertor.INSTANCE.dto2Do(extApiLogDTO);
        ExtApiLogPO extApiLogPO = ExtApiLogConvertor.INSTANCE.dto2Po(extApiLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        extApiLogDomainService.validation(extApiLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(extApiLogPO);
        extApiLogDao.update(extApiLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
    @Override
    public String getObjectType() {
//        return ObjectTypeEnum.EXT_API_LOG.getCode();
        return null;
    }

    @Override
    public List<ExtApiLogVO> invocation(List<ExtApiLogVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    public BaseResponse<Void> create(ExtApiLogDTO extApiLogDTO) {
        // 0.数据转换
        ExtApiLogDO extApiLogDO = ExtApiLogConvertor.INSTANCE.dto2Do(extApiLogDTO);
        ExtApiLogPO extApiLogPO = ExtApiLogConvertor.INSTANCE.dto2Po(extApiLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        extApiLogDomainService.validation(extApiLogDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(extApiLogPO);
        extApiLogDao.insertWithPrimaryKey(extApiLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    /**
     * 创建API调用日志
     * @param apiConfigVO API配置
     * @param params 请求参数
     * @param parentLog 父日志(为null时创建主日志)
     * @param requestHeaders 请求头(子日志必填)
     * @param requestParams 请求参数(子日志必填)
     */
    public ExtApiLogDTO createLog(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                  ExtApiLogDTO parentLog, String requestHeaders, String requestParams) {
        ExtApiLogDTO log = new ExtApiLogDTO();
        log.setId(UUIDUtil.getUUID());
        log.setConfigId(apiConfigVO.getId());
        log.setEnabled(YesOrNoEnum.YES.getCode());
        log.setStatus(DcpConstants.TASKS_STATUS_RUNNING);
        // 获取当前日期时间，格式为 yyyyMMddHHmmss
        String dateTimeStr = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        // 生成 Snowflake ID
        long snowflakeId = IdUtil.createSnowflake(1, 1).nextId();
        String serialNumber = dateTimeStr + String.format("%016d", snowflakeId);
        log.setBatchNo(serialNumber);
        if (parentLog == null) {
            // 创建主日志
            log.setTriggerType(Objects.isNull(params.get("triggerType")) ?
                    DcpConstants.TASKS_AUTO_TRIGGER : DcpConstants.TASKS_MANUAL_TRIGGER);
            log.setParentId(Objects.isNull(params.get("parentId")) ?
                    null : String.valueOf(params.get("parentId")));
        } else {
            // 创建子日志
            log.setParentId(parentLog.getId());
            log.setRequestType(parentLog.getRequestType());
            log.setRequestHeaders(requestHeaders);
            // 发送请求报文到MQ
            if (StringUtils.isNotBlank(requestParams)) {
                try {
                    extApiLogMessageProducer.sendRequestMessage(log.getId(), requestParams);
                    log.debug("发送请求报文到MQ成功，logId: {}", log.getId());
                } catch (Exception e) {
                    log.error("发送请求报文到MQ失败，logId: {}, error: {}", log.getId(), e.getMessage());
                }
            }
        }
        log.setRequestTime(new Date());
        this.create(log);
        return log;
    }

    /**
     * 更新API调用日志
     * @param log 日志对象
     * @param response HTTP响应(可选)
     * @param dataCount 数据量(可选)
     * @param status 状态(可选，默认成功)
     */
    public void updateResponse(ExtApiLogDTO log, ResponseEntity<String> response,
                               Integer dataCount, String status) {
        log.setResponseTime(new Date());

        // 更新响应信息
        if (response != null) {
            log.setResponseHeaders(String.valueOf(response.getHeaders()));
            log.setResponseStatus(String.valueOf(response.getStatusCodeValue()));

            // 发送响应报文到MQ而不是直接存储到MySQL
            String responseBody = response.getBody();
            if (StringUtils.isNotBlank(responseBody)) {
                try {
                    extApiLogMessageProducer.sendResponseMessage(log.getId(), responseBody);
                    log.debug("发送响应报文到MQ成功，logId: {}", log.getId());
                } catch (Exception e) {
                    log.error("发送响应报文到MQ失败，logId: {}, error: {}", log.getId(), e.getMessage());
                }
            }
        }

        // 更新数据量
        if (dataCount != null) {
            log.setResolveCount(dataCount);
        }

        // 更新状态
        log.setStatus(status != null ? status :
                (response != null && HttpStatus.OK.value() == response.getStatusCodeValue() ?
                        DcpConstants.TASKS_STATUS_SUCCESS : DcpConstants.TASKS_STATUS_ERROR));

        this.update(log);
    }

}
