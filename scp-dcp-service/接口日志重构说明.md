# 接口日志报文存储重构说明

## 重构目标

将接口日志的请求体（requestBody）和响应体（responseBody）从MySQL迁移到MongoDB存储，通过RabbitMQ进行异步处理，实现以下功能：

1. MySQL只保存基本日志信息，不再存储大量报文数据
2. 报文数据通过RabbitMQ异步发送到MongoDB
3. 对于响应体中的数组数据，将每个数组元素单独存储到MongoDB
4. 保持原有接口的兼容性

## 架构变更

### 原架构
```
接口调用 -> 直接保存到MySQL (包含requestBody和responseBody)
```

### 新架构
```
接口调用 -> MySQL保存基本信息 + RabbitMQ发送报文 -> MongoDB存储报文数据
```

## 主要变更内容

### 1. 数据库变更

#### MySQL表结构变更
- 移除 `ext_api_log` 表中的 `request_body` 和 `response_body` 字段
- 执行迁移脚本：`V1.0.1__remove_body_fields.sql`

#### MongoDB新增集合
- 新增 `ext_api_log_message` 集合存储报文数据
- 支持数组数据的拆分存储

### 2. 新增组件

#### MongoDB相关
- `ExtApiLogMessageDTO`: MongoDB文档实体类
- `ExtApiLogMessageRepository`: MongoDB数据访问接口
- `ExtApiLogMessageService`: 报文数据处理服务
- `MongoConfig`: MongoDB配置类

#### RabbitMQ相关
- `ExtApiLogMessageProducer`: 消息生产者
- `ExtApiLogMessageConsumer`: 消息消费者
- `RabbitMqConfig`: RabbitMQ配置类
- `ExtApiLogMqConstants`: MQ常量定义

### 3. 修改的组件

#### 实体类变更
- `ExtApiLogPO`: 移除requestBody和responseBody字段
- `ExtApiLogDTO`: 移除requestBody和responseBody字段

#### 服务层变更
- `ExtApiLogServiceImpl`: 
  - 新增支持requestBody参数的createLog方法
  - 修改updateResponse方法，将报文发送到MQ而非直接存储
  - 集成消息生产者

#### 数据访问层变更
- `ExtApiLogDao.xml`: 移除所有SQL语句中的request_body和response_body字段

### 4. 配置变更

#### 依赖添加
```xml
<!-- MongoDB依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-mongodb</artifactId>
</dependency>

<!-- RabbitMQ依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-amqp</artifactId>
</dependency>
```

#### 配置文件
```yaml
spring:
  data:
    mongodb:
      host: ***********
      port: 27017
      username: bpimadm
      password: 'WOVQ.dL9z+2oHBW'
      database: mongosit
  rabbitmq:
    host: ***********
    port: 5672
    username: bpimadm
    password: '[wunNK5?BDjVDe|'
```

## 数据处理逻辑

### 请求体处理
- 请求体数据直接存储到MongoDB，每个请求对应一条记录

### 响应体处理
- 检测响应体结构，如果包含 `data.message` 数组
- 将数组中的每个对象单独存储到MongoDB
- 每个对象记录包含：
  - `logId`: 关联的MySQL日志记录ID
  - `messageType`: RESPONSE
  - `messageData`: 单个对象的JSON数据
  - `arrayIndex`: 在数组中的索引位置

### 示例数据流

#### 输入响应体
```json
{
    "data": {
        "message": [
            {"itemCode": "BRA5980G", "quantity": 50},
            {"itemCode": "BRA3656G", "quantity": 50}
        ]
    }
}
```

#### MongoDB存储结果
```json
// 记录1
{
    "logId": "log-001",
    "messageType": "RESPONSE", 
    "messageData": "{\"itemCode\": \"BRA5980G\", \"quantity\": 50}",
    "arrayIndex": 0
}

// 记录2  
{
    "logId": "log-001",
    "messageType": "RESPONSE",
    "messageData": "{\"itemCode\": \"BRA3656G\", \"quantity\": 50}", 
    "arrayIndex": 1
}
```

## 使用方式

### 创建日志（带请求体）
```java
ExtApiLogDTO log = extApiLogService.createLog(
    apiConfigVO, 
    params, 
    parentLog, 
    requestHeaders, 
    requestParams, 
    requestBody  // 新增参数
);
```

### 更新响应（自动处理响应体）
```java
extApiLogService.updateResponse(log, response, dataCount, status);
// 响应体会自动发送到MQ并存储到MongoDB
```

### 查询报文数据
```java
// 查询某个日志的所有报文数据
List<ExtApiLogMessageDTO> messages = extApiLogMessageService.getMessagesByLogId(logId);

// 查询某个日志的响应报文数据
List<ExtApiLogMessageDTO> responseMessages = 
    extApiLogMessageService.getMessagesByLogIdAndType(logId, "RESPONSE");
```

## 测试

运行测试类 `ExtApiLogMessageTest` 验证功能：
- `testRequestMessage()`: 测试请求体处理
- `testResponseMessage()`: 测试响应体处理  
- `testProcessResponseData()`: 测试数组数据拆分

## 注意事项

1. **向后兼容**: 原有的createLog方法仍然可用，只是不会处理请求体
2. **异步处理**: 报文数据通过MQ异步处理，不影响接口响应性能
3. **错误处理**: 如果MQ或MongoDB出现问题，不会影响MySQL的日志记录
4. **数据一致性**: 通过logId关联MySQL和MongoDB中的数据
5. **性能优化**: 大量报文数据不再存储在MySQL中，提升查询性能

## 部署步骤

1. 确保MongoDB和RabbitMQ服务可用
2. 更新配置文件中的连接信息
3. 执行数据库迁移脚本
4. 部署新版本代码
5. 验证功能正常运行
