package com.yhl.scp.mps.common.controller;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPlanOrderQuery;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesFeedBack;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldChangeTime;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldToolingGroupDir;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesReportFeedback;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesSubInventoryCargoLocation;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipDao;
import com.yhl.scp.mps.capacityBalance.service.CapacityBalanceAlgorithmExecuteService;
import com.yhl.scp.mps.capacityBalance.service.CapacityBalanceVersionService;
import com.yhl.scp.mps.capacityBalance.service.CapacitySupplyRelationshipService;
import com.yhl.scp.mps.capacityBalance.vo.CapacityBalanceVersionVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.demand.service.OutsourceTransferDemandDetailService;
import com.yhl.scp.mps.demand.vo.OutsourceTransferDemandDetailVO;
import com.yhl.scp.mps.feedback.service.MesFeedbackService;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.fixtureRelation.service.ProductFixtureRelationService;
import com.yhl.scp.mps.fixtureRelation.vo.ProductFixtureRelationVO;
import com.yhl.scp.mps.highValueMaterials.dto.MpsHighValueMaterialsDTO;
import com.yhl.scp.mps.highValueMaterials.service.MpsHighValueMaterialsService;
import com.yhl.scp.mps.highValueMaterials.vo.MpsHighValueMaterialsVO;
import com.yhl.scp.mps.model.service.MoldChangeTimeService;
import com.yhl.scp.mps.nakedGlass.dto.NakedGlassDTO;
import com.yhl.scp.mps.nakedGlass.service.NakedGlassService;
import com.yhl.scp.mps.nakedGlass.vo.NakedGlassVO;
import com.yhl.scp.mps.operationPublished.service.DemandPublishedService;
import com.yhl.scp.mps.operationPublished.service.OperationInputPublishedService;
import com.yhl.scp.mps.operationPublished.service.OperationPublishedService;
import com.yhl.scp.mps.operationPublished.service.WorkOrderPublishedService;
import com.yhl.scp.mps.operationPublished.vo.DemandPublishedVO;
import com.yhl.scp.mps.operationPublished.vo.OperationInputPublishedVO;
import com.yhl.scp.mps.operationPublished.vo.OperationPublishedVO;
import com.yhl.scp.mps.operationPublished.vo.WorkOrderPublishedVO;
import com.yhl.scp.mps.plan.dto.MasterPlanRelationLogDTO;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import com.yhl.scp.mps.plan.service.*;
import com.yhl.scp.mps.plan.vo.*;
import com.yhl.scp.mps.product.dto.ChainLineInventoryLogDTO;
import com.yhl.scp.mps.product.service.ChainLineInventoryLogService;
import com.yhl.scp.mps.product.service.ProductAdvanceBatchRuleService;
import com.yhl.scp.mps.product.vo.ProductAdvanceBatchRuleVO;
import com.yhl.scp.mps.reportingFeedback.service.MpsProReportingFeedbackService;
import com.yhl.scp.mps.resource.service.ResourceService;
import com.yhl.scp.mps.rule.dto.AlgorithmConstraintRuleDTO;
import com.yhl.scp.mps.rule.enums.AlgorithmConstraintRuleEnum;
import com.yhl.scp.mps.rule.service.AlgorithmConstraintRuleService;
import com.yhl.scp.mps.subInventoryCargoLocation.service.SubInventoryCargoLocationService;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import com.yhl.scp.sds.extension.order.vo.OperationInputVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.extension.pegging.dto.DemandDTO;
import com.yhl.scp.sds.extension.pegging.dto.FulfillmentDTO;
import com.yhl.scp.sds.extension.pegging.dto.SupplyDTO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.extension.pegging.vo.FulfillmentVO;
import com.yhl.scp.sds.extension.pegging.vo.SupplyVO;
import com.yhl.scp.sds.order.service.OperationInputService;
import com.yhl.scp.sds.order.service.OperationService;
import com.yhl.scp.sds.order.service.WorkOrderService;
import com.yhl.scp.sds.pegging.service.DemandService;
import com.yhl.scp.sds.pegging.service.FulfillmentService;
import com.yhl.scp.sds.pegging.service.SupplyService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <code>DfpFeignController</code>
 * <p>
 * DfpFeignController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-22 14:40:17
 */
@Slf4j
@Api(tags = "Feign")
@RestController
public class MpsFeignController implements MpsFeign {

    @Resource
    MasterPlanVersionService masterPlanVersionService;
    @Resource
    MasterPlanIssuedDataService masterPlanIssuedDataService;
    @Resource
    private MpsHighValueMaterialsService highValueMaterialsService;
    @Resource
    private SubInventoryCargoLocationService subInventoryCargoLocationService;
    @Resource
    private ResourceService resourceService;
    @Resource
    private CapacityBalanceVersionService capacityBalanceVersionService;
    @Resource
    private CapacitySupplyRelationshipService capacitySupplyRelationshipService;
    @Resource
    private MpsProReportingFeedbackService mpsProReportingFeedbackService;

    @Resource
    private DemandService demandService;

    @Resource
    private FulfillmentService fulfillmentService;

    @Resource
    private SupplyService supplyService;

    @Resource
    private MoldChangeTimeService moldChangeTimeService;

    @Resource
    private ChainLineInventoryLogService chainLineInventoryLogService;

    @Resource
    private ProductAdvanceBatchRuleService productAdvanceBatchRuleService;

    @Resource
    private NakedGlassService nakedGlassService;

    @Resource
    private MasterPlanService masterPlanService;

    @Resource
    private AlgorithmConstraintRuleService algorithmConstraintRuleService;
    @Resource
    private MasterPlanRelationService masterPlanRelationService;

    @Resource
    private MasterPlanRelationLogService masterPlanRelationLogService;

    @Resource
    private WorkOrderPublishedService workOrderPublishedService;

    @Resource
    private OperationPublishedService operationPublishedService;

    @Resource
    private DemandPublishedService demandPublishedService;
    @Resource
    private CapacityBalanceAlgorithmExecuteService algorithmExecuteService;
    @Resource
    private CapacitySupplyRelationshipDao capacitySupplyRelationshipDao;

    @Resource
    private OperationInputPublishedService operationInputPublishedService;

    @Resource
    private MasterPlanPublishedLogService masterPlanPublishedLogService;

    @Resource
    private OutsourceTransferDemandDetailService outsourceTransferDemandDetailService;

    @Resource
    private WorkOrderService workOrderService;

    @Resource
    private OperationService operationService;

    @Resource
    private OperationInputService operationInputService;
    @Resource
    private OperationTaskExtDao operationTaskExtDao;
    @Resource
    private MesFeedbackService mesFeedbackService;

    @Resource
    private ProductFixtureRelationService productFixtureRelationService;

    @Override
    public BaseResponse<Void> createNewData(String scenario, List<MpsHighValueMaterialsDTO> mpsHighValueMaterialsDTOs) {
        return highValueMaterialsService.createNewData(scenario, mpsHighValueMaterialsDTOs);
    }

    @Override
    public List<MpsHighValueMaterialsVO> queryByParams(String scenario, Map<String, Object> params) {
        return highValueMaterialsService.selectByParams(params);
    }

    public BaseResponse<Void> syncSubInventoryCargoLocation(String scenario, List<MesSubInventoryCargoLocation> subInventoryCargoLocations) {
        return subInventoryCargoLocationService.syncSubInventoryCargoLocation(scenario, subInventoryCargoLocations);
    }

    @Override
    public List<SubInventoryCargoLocationVO> queryByParams1(String scenario, Map<String, Object> params) {
        return subInventoryCargoLocationService.selectByParams(params);
    }

    @Override
    public List<SubInventoryCargoLocationVO> queryByFreightSpaces(String scenario, List<String> spaceList, String stockPointType) {
        return subInventoryCargoLocationService.selectByBatchCodeAndStockType(spaceList, stockPointType);
    }

    @Override
    public CapacityBalanceVersionVO selectLatestCapacityBalanceVersionCode(String scenario) {
        return capacityBalanceVersionService.selectLatestVersionCode();
    }

    @Override
    public List<CapacitySupplyRelationshipVO> selectCapacitySupplyRelationshipByVersionCode(String scenario, String versionCode) {
        return capacitySupplyRelationshipService.selectByParams(ImmutableMap.of("versionId", versionCode));
    }

    @Override
    public List<MasterPlanIssuedDataVO> getMasterPlanIssuedDataByVersionId(String masterPlanVersionId) {
        return masterPlanIssuedDataService.selectByParams(ImmutableMap.of("masterPlanVersionId", masterPlanVersionId));
    }

    @Override
    public MasterPlanVersionVO getLatestPublishedMpsVersion() {
        return masterPlanVersionService.getLatestPublishedMpsVersion();
    }

    @Override
    public MasterPlanVersionVO getLatestPublishedMpsVersionPublished() {
        return masterPlanVersionService.getLatestPublishedMpsVersionPublished();
    }

    @Override
    public BaseResponse<Void> doSyncReportFeedback(String scenario, List<MesReportFeedback> feedbacks) {
        return mpsProReportingFeedbackService.handleReportingFeedback(feedbacks);
    }

    @Override
    public List<DemandVO> getDemandListByParams(String scenario, Map<String, Object> params) {
        return demandService.selectByParams(params);
    }

    @Override
    public BaseResponse<Void> addDemand(List<DemandDTO> demandDTOList) {
        demandService.doCreateBatch(demandDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> updateDemand(String scenario, List<DemandDTO> demandDTOList) {
        demandService.doUpdateBatch(demandDTOList, false);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public List<FulfillmentVO> getFulfillmentListByParams(String scenario, Map<String, Object> params) {
        return fulfillmentService.selectByParams(params);
    }

    @Override
    public BaseResponse<Void> addFulfillment(List<FulfillmentDTO> fulfillmentDTOList) {
        fulfillmentService.doCreateBatch(fulfillmentDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> updateFulfillment(String scenario, List<FulfillmentDTO> fulfillmentDTOList) {
        fulfillmentService.doUpdateBatch(fulfillmentDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> deleteFulfillmentBySupplyId(List<String> idList) {
        fulfillmentService.doDeleteBySupplyIds(idList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> deleteFulfillmentByDemandId(List<String> idList) {
        fulfillmentService.doDeleteByDemandIds(idList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public List<SupplyVO> getSupplyListByParams(String scenario, Map<String, Object> params) {
        return supplyService.selectByParams(params);
    }

    @Override
    public BaseResponse<Void> addSupply(String scenario, List<SupplyDTO> supplyDTOList) {
        supplyService.doCreateBatch(supplyDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> updateSupply(String scenario, List<SupplyDTO> supplyDTOList) {
        supplyService.doUpdateBatch(supplyDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> syncMoldChangeTime(String scenario, List<MesMoldChangeTime> moldChangeTimes) {
        return moldChangeTimeService.syncMoldChangeTime(scenario, moldChangeTimes);

    }

    @Override
    public List<ProductAdvanceBatchRuleVO> selectAllProductAdvanceBatchRule() {
        return productAdvanceBatchRuleService.selectAll();
    }

    @Override
    public BaseResponse<Void> handleChainLine(String scenario, List<ChainLineInventoryLogDTO> chainLineInventoryLogDTOList) {
        BaseResponse<Void> voidBaseResponse = chainLineInventoryLogService.handleChainLine(chainLineInventoryLogDTOList);
        return voidBaseResponse;
    }

    @Override
    public BaseResponse<Void> syncNakedGlassData(String scenario, List<NakedGlassDTO> list) {
        return nakedGlassService.syncNakedGlassData(list);
    }

    @Override
    public List<NakedGlassVO> selectNakedGlassByFinishedProducts(List<String> productCodes) {
        return nakedGlassService.selectNakedGlassByFinishedProducts(productCodes);
    }

    @Override
    public List<CapacitySupplyRelationshipVO> selectCapacitySupplyRelationshipByParams(String scenario, Map<String, Object> params) {
        return capacitySupplyRelationshipService.selectByParams(params);
    }

    @Override
    public AlgorithmConstraintRuleDTO getAlgorithmConstraintRuleMap(@RequestHeader("scenario") String scenario) {
        Map<String, Pair<String, String>> algorithmConstraintRuleMap = algorithmConstraintRuleService.getAlgorithmConstraintRuleMap();
        AlgorithmConstraintRuleDTO dto = new AlgorithmConstraintRuleDTO();
        if (algorithmConstraintRuleMap.containsKey(AlgorithmConstraintRuleEnum.COMMON_RULE_13.getCode())) {
            Pair<String, String> pair = algorithmConstraintRuleMap.get(AlgorithmConstraintRuleEnum.COMMON_RULE_13.getCode());
            dto.setRuleValue(pair.getRight());
        }
        return dto;
    }

    @Override
    public BaseResponse<Void> handlePlanOrder(String scenario, List<ErpPlanOrderQuery> list) {
        return masterPlanRelationService.sync(scenario, list);
    }

    @Override
    public BaseResponse<Void> syncMasterPlanRelation(String scenario, List<MasterPlanRelationLogDTO> list) {
        return masterPlanRelationLogService.syncMasterPlanRelation(list);
    }

    @Override
    public List<MasterPlanWorkOrderBodyVO> getMasterPlan(MasterPlanReq masterPlanReq) {
        return masterPlanService.getMasterPlan(masterPlanReq);
    }

    @Override
    public List<DeliveryPlanGeneralViewVO> getDeliveryPlanGeneralViewVO(MasterPlanReq masterPlanReq) {
        return masterPlanService.getDeliveryPlanGeneralView(masterPlanReq);
    }

    @Override
    public List<WorkOrderPublishedVO> selectWorkOrderPublishedByParams(String scenario, Map<String, Object> params) {
        return workOrderPublishedService.selectByParams(params);
    }

    @Override
    public List<OperationPublishedVO> selectOperationPublishedByParams(String scenario, Map<String, Object> params) {
        return operationPublishedService.selectByParams(params);
    }

    @Override
    public List<DemandPublishedVO> selectDemandPublishedByParams(String scenario, Map<String, Object> params) {
        return demandPublishedService.selectByParams(params);
    }

    public void doRefreshCapacityBalance(String scenario, String planPeriod, String capacityPeriod) {
        DynamicDataSourceContextHolder.setDataSource(scenario);
        if (StringUtils.isEmpty(scenario) || StringUtils.isEmpty(planPeriod) || StringUtils.isEmpty(capacityPeriod)) {
            return;
        }
        //查询当前产能平衡计算结果对应的需求预测月份
        String latestPlanPeriod = capacitySupplyRelationshipDao.selectLatestPlanPeriod();
        if (latestPlanPeriod != null) {
            latestPlanPeriod = latestPlanPeriod.replace("-", "");
            if (latestPlanPeriod.equals(planPeriod)) {
                //执行产能平衡计算
                algorithmExecuteService.capacityBalanceExecuteLock(capacityPeriod);
                //发布版本
                capacityBalanceVersionService.publishVersionLock();
            }
        }
        DynamicDataSourceContextHolder.clearDataSource();
    }

    @Override
    public List<OperationInputPublishedVO> selectOperationInputPublishedByParams(String scenario, Map<String, Object> params) {
        return operationInputPublishedService.selectByParams(params);
    }

    @Override
    public String selectMasterPlanPublishedLogIdByOperatorId() {
        return masterPlanPublishedLogService.selectNewestLogIdByOperatorId(null);
    }

    @Override
    public List<OutsourceTransferDemandDetailVO> selectOutsourceTransferDemandDetailByParams(String scenario, Map<String, Object> params) {
        return outsourceTransferDemandDetailService.selectByParams(params);
    }

    @Override
    public String selectWeekMaxVersionTime(String scenario) {
        return capacityBalanceVersionService.selectWeekMaxVersionTime();
    }

    @Override
    public List<WorkOrderVO> selectWorkOrderByParams(String scenario, Map<String, Object> params) {
        return workOrderService.selectByParams(params);
    }

    @Override
    public List<OperationVO> selectOperationByParams(String scenario, Map<String, Object> params) {
        return operationService.selectByParams(params);
    }

    @Override
    public List<DemandVO> selectDemandByParams(String scenario, Map<String, Object> params) {
        return demandService.selectByParams(params);
    }

    @Override
    public List<OperationInputVO> selectOperationInputByParams(String scenario, Map<String, Object> params) {
        return operationInputService.selectByParams(params);
    }

    @Override
    public List<MasterPlanTaskVO> selectByMasterReq(String scenario, MasterPlanReq masterPlanReq) {
        return operationTaskExtDao.selectByMasterReq(masterPlanReq);
    }

    @Override
    public void handleMesFeedBack(String scenario, List<MesFeedBack> mesFeedBacks) {
        mesFeedbackService.doHandleMesFeedBack(mesFeedBacks);
    }

    @Override
    public BaseResponse<Void> synMoldToolingGroupDir(String tenantId) {
        return productFixtureRelationService.synMoldToolingGroupDir(tenantId);
    }

    @Override
    public BaseResponse<Void> handleMoldToolingGroupDir(String scenario, List<MesMoldToolingGroupDir> o) {
        return productFixtureRelationService.handleMoldToolingGroupDir(o);
    }

    @Override
    public Map<String, String> selectMoldToolingRelationsByProductCodes(String scenario, List<String> productCodes) {
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("productCodes", productCodes);
        params.put("enabled", YesOrNoEnum.YES.getCode());
        List<ProductFixtureRelationVO> fixtureRelations = productFixtureRelationService.selectByParams(params);
        if (CollectionUtils.isEmpty(fixtureRelations)) {
            return MapUtil.newHashMap();
        }
        return fixtureRelations.stream().collect(
                Collectors.toMap(ProductFixtureRelationVO::getProductCode,
                        ProductFixtureRelationVO::getStandardResourceId,
                        (v1, v2) -> v1));
    }

    @Override
    public List<ProductFixtureRelationVO> selectMoldToolingRelationCountByProductCodes(String scenario, List<String> productCodes) {
        Map<String, Object> params = MapUtil.newHashMap();
        if (CollectionUtils.isNotEmpty(productCodes)) {
            params.put("productCodes", productCodes);
        }
        params.put("enabled", YesOrNoEnum.YES.getCode());
        return Optional.ofNullable(productFixtureRelationService.selectByParams(params)).orElse(Lists.newArrayList());
    }
}
