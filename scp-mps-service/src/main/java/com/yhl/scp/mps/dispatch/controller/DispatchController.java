package com.yhl.scp.mps.dispatch.controller;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableMap;
import com.yhl.aps.api.runner.APSOutput;
import com.yhl.aps.api.runner.APSRunner;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.common.enums.AlgorithmLogStatusEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.mps.algorithm.strategy.impl.CancelPlanCommand;
import com.yhl.scp.mps.algorithm.strategy.impl.FeedbackCommand;
import com.yhl.scp.mps.dispatch.service.DispatchService;
import com.yhl.scp.mps.domain.algorithm.RzzBaseAlgorithmDataService;
import com.yhl.scp.mps.domain.dispatch.IAmsSchedule;
import com.yhl.scp.mps.domain.dispatch.model.context.MpsAnalysisContext;
import com.yhl.scp.mps.domain.sync.IWorkOrderSync;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.order.service.OperationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Api(tags = "MPS-调度排程控制器")
@RestController
@RequestMapping("/dispatch")
@Slf4j
public class DispatchController extends BaseController {

    @Resource
    private DispatchService dispatchService;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private IAmsSchedule amsSchedule;

    @Resource
    private IWorkOrderSync workOrderSync;

    @Resource
    private OperationService operationService;

    @Resource
    private CancelPlanCommand cancelPlanCommand;

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private FeedbackCommand feedbackCommand;

    @ApiOperation(value = "优化计算-开始")
    @PostMapping(value = "/create")
    @BusinessMonitorLog(businessCode = "自动排产", moduleCode = "MPS", businessFrequency = "DAY")
    public BaseResponse createSchedule(@RequestBody Map<String ,List<String>> params) throws InterruptedException {
        String userId = SystemHolder.getUserId();
        String lockKey = "MPS-Dispatcher-" + userId;
        RLock lock = redissonClient.getLock(lockKey);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock(0, 5, TimeUnit.SECONDS);
            if (!isLocked) {
                return BaseResponse.error("请勿重复提交请求");
            }
            return dispatchService.doCreate(null, params);
        } finally {
            try {
                if (isLocked) {
                    lock.unlock();
                }
            } catch (Exception e) {
                log.error("释放锁失败: {}", e.getMessage(), e);
            }
        }
    }

    @ApiOperation(value = "优化计算-AMS测试")
    @PostMapping(value = "/ams")
    @Transactional
    public BaseResponse ams(@RequestBody List<String> ids) {
        List<OperationVO> operationVOS = operationService.selectByParams(ImmutableMap.of("parentIdIsNotNull",
                YesOrNoEnum.YES.getCode()));
        MpsAnalysisContext mpsAnalysisContext = new MpsAnalysisContext();
        if (CollectionUtils.isNotEmpty(ids)) {
            operationVOS = operationVOS.stream().filter(p -> ids.contains(p.getOrderId())).collect(Collectors.toList());
        }
        // 取消计划命令
        cancelPlanCommand.doCancelAmsOperationPlan(operationVOS);
        List<String> wids = operationVOS.stream().map(OperationVO::getOrderId).collect(Collectors.toList());
        mpsAnalysisContext.setWorkOrderIds(wids.stream().distinct().collect(Collectors.toList()));
        AlgorithmLog algorithmLog = new AlgorithmLog();
        algorithmLog.setCreator("8d62ab0f-018d-72b118fe-8a87cdd8-0029");
        mpsAnalysisContext.setAlgorithmLog(algorithmLog);
        mpsAnalysisContext.setAlgorithmStepLogDTOList(new ArrayList<>());
        amsSchedule.doAmsSchedule(algorithmLog, mpsAnalysisContext);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "设置待排工序")
    @PostMapping(value = "/adjustPlan")
    @Transactional
    public BaseResponse adjustPlan(@RequestBody List<String> plan) {
        List<OperationVO> operationVOS = operationService.selectByParams(ImmutableMap.of("ids", plan));
        List<String> orderIds = operationVOS.stream().map(OperationVO::getOrderId).collect(Collectors.toList());
        List<OperationVO> operationAll = operationService.selectByParams(ImmutableMap.of("orderIds", orderIds));
        List<String> parentOperationIds = operationVOS.stream().map(OperationVO::getParentId).collect(Collectors.toList());
        // 取消关键工序以外的所有前后工序
        List<OperationVO> cancelOperation = operationAll.stream().filter(p -> StrUtil.isNotEmpty(p.getParentId()) && !plan.contains(p.getId())).collect(Collectors.toList());
        cancelPlanCommand.doCancelAmsOperationPlan(cancelOperation);
        List<OperationVO> waitingSchedule = operationAll.stream().filter(p -> StrUtil.isEmpty(p.getParentId()) && !parentOperationIds.contains(p.getId())).collect(Collectors.toList());
        MpsAnalysisContext mpsAnalysisContext = new MpsAnalysisContext();
        mpsAnalysisContext.setUseOperationList(waitingSchedule);
        amsSchedule.doAmsSchedule(new AlgorithmLog(), mpsAnalysisContext);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }


    @ApiOperation(value = "数据包排错")
    @PostMapping(value = "/file")
    public BaseResponse file(String cwd) {
        APSRunner apsRunner = new APSRunner();
        APSOutput apsOutput = apsRunner.runFromSerializedInput(cwd);
        System.out.println(apsOutput);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Resource
    private RzzBaseAlgorithmDataService rzzBaseAlgorithmDataService;

    @ApiOperation(value = "优化计算-订单展开+AMS测试")
    @PostMapping(value = "/workOrderAms")
    public BaseResponse workOrderAms() {
//        amsSchedule.doAmsSchedule(new AlgorithmLog(), new MpsAnalysisContext());
        DynamicDataSourceContextHolder.setDataSource("scp_fysh");
        rzzBaseAlgorithmDataService.getAlgorithmData(null,null);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "组装数据执行优化算法")
    @PostMapping(value = "/execute")
    public BaseResponse executeSchedule(@RequestBody AlgorithmLog algorithmLog) {

        log.info("开始组装数据,algorithmLog:" + algorithmLog);
        DynamicDataSourceContextHolder.setDataSource(algorithmLog.getScenario());
        session.setAttribute(Constants.SCENARIO, algorithmLog.getScenario());
        log.info("ScheduleController.executeSchedule scenario1 is " + DynamicDataSourceContextHolder.getDataSource());
        try {
            feedbackCommand.executeCommandLock(algorithmLog);
        } catch (Exception e) {
            log.error("生产反馈算法调用失败：", e);
            algorithmLog.setStatus(AlgorithmLogStatusEnum.FAIL.getCode());
            algorithmLog.setFailMsg("MPS算法调用失败：" + e.getMessage());
            algorithmLog.setEndTime(new Date());
            return BaseResponse.error("生产反馈算法执行失败+" + e.getMessage());
        } finally {
            // 更新ips算法日志调用状态
            ipsFeign.updateAlgorithmLog(algorithmLog);
            log.info("生产反馈算法日志更新完成");
        }
        dispatchService.doSchedule(algorithmLog);
        log.info("ScheduleController.executeSchedule scenario2 is " + DynamicDataSourceContextHolder.getDataSource());
        DynamicDataSourceContextHolder.clearDataSource();
        return new BaseResponse(true, "");
    }

    @ApiOperation(value = "获取算法运行状态")
    @PostMapping(value = "/check")
    public BaseResponse<String> algorithmCheck(@RequestBody AlgorithmLog algorithmLog) throws UnsupportedEncodingException {
        String scenario = algorithmLog.getScenario();
        // 设置当前数据库
        DynamicDataSourceContextHolder.setDataSource(scenario);
        BaseResponse baseResponse = dispatchService.getAlgorithmStatus(algorithmLog);
        String executorStatus = String.valueOf(baseResponse.getData());
        DynamicDataSourceContextHolder.clearDataSource();
        return BaseResponse.success("", executorStatus);
    }

    @ApiOperation(value = "解析MPS算法结果")
    @PostMapping(value = "/result")
    public BaseResponse resultAnalysis(@RequestBody AlgorithmLog algorithmLog) {
        String lockKey = algorithmLog.getId();
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (!lock.tryLock(0, 200, TimeUnit.SECONDS)) {
                log.error("重复的解析MPS请求：{}", lockKey);
                return BaseResponse.error("重复的解析MPS请求");
            }
            return dispatch(algorithmLog);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 保留中断状态
            log.error("线程被中断: {}", e.getMessage(), e);
            return BaseResponse.error("请求处理被中断");
        } finally {
            unlock(lock);
        }
    }

    private BaseResponse dispatch(AlgorithmLog algorithmLog) {
        String scenario = algorithmLog.getScenario();
        String executionNumber = algorithmLog.getExecutionNumber();
        try {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            session.setAttribute(Constants.SCENARIO, scenario);
            log.info("开始算法结果解析，executionNumber is {}", executionNumber);
            algorithmLog.setStatus(AlgorithmLogStatusEnum.SUCCESS.getCode());
            return dispatchService.resultAnalysis(algorithmLog);
        } catch (Exception e) {
            algorithmLog.setFailMsg(e.getMessage());
            algorithmLog.setStatus(AlgorithmLogStatusEnum.FAIL.getCode());
            log.error("ScheduleController.resultAnalysis", e);
            return BaseResponse.error("算法解析失败: {}", e.getMessage());
        } finally {
            algorithmLog.setEndTime(new Date());
            ipsFeign.updateAlgorithmLog(algorithmLog);
            DynamicDataSourceContextHolder.clearDataSource();
        }
    }

    private void unlock(RLock lock) {
        if (lock != null && lock.isHeldByCurrentThread()) {
            try {
                lock.unlock();
            } catch (Exception e) {
                log.error("释放锁失败: {}", e.getMessage(), e);
            }
        }
    }

}
