package com.yhl.scp.mrp.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <code>CoatingWarningEnum</code>
 * <p>
 * 镀膜预警原因枚举
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-30 14:30:48
 */
public enum CoatingWarningReasonEnum implements CommonEnum {

    COATING_ROUTING("COATING_ROUTING", "原片新鲜度"),
    COATING_DATE("COATING_DATE", "原片新鲜度");

    private String code;

    private String desc;

    CoatingWarningReasonEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    void setCode(String code) {
        this.code = code;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
