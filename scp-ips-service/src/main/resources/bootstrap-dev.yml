nacos-server-addr: *************:8848
spring:
  cloud:
    nacos:
      discovery:
        enabled: false
        server-addr: ${nacos-server-addr}
        namespace: bpim-dev
      config:
        server-addr: ${nacos-server-addr}
        namespace: bpim-dev
        group: DEFAULT_GROUP
        prefix: scp-ips-service
        file-extension: yaml
        shared-configs:
          - dataId: datasource.yaml
            group: DEFAULT_GROUP
            refresh: true
          - dataId: redis.yaml
            group: DEFAULT_GROUP
            refresh: true

ips:
  feign:
    url: ${ips.feign.test.url}
    local:
      url: "http://localhost:8761"
    test:
      url: "https://bpim-test.fuyaogroup.com/api/ips"

mds:
  feign:
    url: ${mds.feign.test.url}
    local:
      url: "http://localhost:8762"
    test:
      url: "https://bpim-test.fuyaogroup.com/api/mds"

dfp:
  feign:
    url: ${dfp.feign.test.url}
    local:
      url: "http://localhost:8764"
    test:
      url: "https://bpim-test.fuyaogroup.com/api/dfp"

mps:
  feign:
    url: ${mps.feign.test.url}
    local:
      url: "http://localhost:8766"
    test:
      url: "https://bpim-test.fuyaogroup.com/api/mps"

mrp:
  feign:
    url: ${mrp.feign.test.url}
    local:
      url: "http://localhost:8767"
    test:
      url: "https://bpim-test.fuyaogroup.com/api/mrp"

das:
  feign:
    url: ${das.feign.test.url}
    local:
      url: "http://localhost:8768"
    test:
      url: "https://bpim-test.fuyaogroup.com/api/das"

dcp:
  feign:
    url: ${dcp.feign.test.url}
    local:
      url: "http://localhost:8769"
    test:
      url: "https://bpim-test.fuyaogroup.com/api/dcp"