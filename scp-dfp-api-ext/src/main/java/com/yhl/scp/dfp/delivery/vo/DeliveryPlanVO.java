package com.yhl.scp.dfp.delivery.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <code>DeliveryPlanVO</code>
 * <p>
 * 发货计划表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 13:51:11
 */
@ApiModel(value = "发货计划表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryPlanVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -13251316386759664L;

    /**
     * 版本id
     */
    @ApiModelProperty(value = "版本id")
    @FieldInterpretation(value = "版本id")
    private String versionId;

    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    @FieldInterpretation(value = "需求类型")
    private String demandCategory;

    /**
     * 版本id
     */
    @ApiModelProperty(value = "版本编码")
    @FieldInterpretation(value = "版本编码")
    private String versionCode;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;

    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    @FieldInterpretation(value = "主机厂名称")
    private String oemName;

    /**
     * 供应类型
     */
    @ApiModelProperty(value = "供应类型")
    @FieldInterpretation(value = "供应类型")
    private String supplyType;
    /**
     * 贸易类型
     */
    @ApiModelProperty(value = "贸易类型")
    @FieldInterpretation(value = "贸易类型")
    private String tradeType;
    
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @FieldInterpretation(value = "车型编码")
    private String vehicleModelCode;
    
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;
    /**
     * 装车位置
     */
    @ApiModelProperty(value = "装车位置")
    @FieldInterpretation(value = "装车位置")
    private String loadingPosition;
    /**
     * 运输路线id
     */
    @ApiModelProperty(value = "运输路线id")
    @FieldInterpretation(value = "运输路线id")
    private String transportationRouteId;

    /**
     * 发布状态
     */
    @ApiModelProperty(value = "发布状态")
    @FieldInterpretation(value = "发布状态")
    private String publishStatus;

    /**
     * 运输路线名称
     */
    @ApiModelProperty(value = "运输路线名称")
    @FieldInterpretation(value = "运输路线名称")
    private String routingName;
    /**
     * 累计在途
     */
    @ApiModelProperty(value = "累计在途")
    @FieldInterpretation(value = "累计在途")
    private Integer inTransitTotal;

    /**
     * 单箱片数
     */
    @ApiModelProperty(value = "单箱片数")
    @FieldInterpretation(value = "单箱片数")
    private Integer piecePerBox;

    @ApiModelProperty(value = "发货计划数据明细")
    @FieldInterpretation(value = "发货计划数据明细")
    private List<DeliveryPlanDetailVO> detailList;

    @ApiModelProperty(value = "发货计划时间列表")
    private List<Date> dateList;

    /**
     * 版本状态
     */
    @ApiModelProperty(value = "版本状态")
    @FieldInterpretation(value = "版本状态")
    private String versionStatus;
    
    /**
     * 需求数量(发货明细当天需求数量)
     */
    @ApiModelProperty(value = "需求数量")
    @FieldInterpretation(value = "需求数量")
    private Integer demandQuantity;
    
    /**
     * 计划员
     */
    @ApiModelProperty(value = "计划员")
    @FieldInterpretation(value = "计划员")
    private String orderPlanner;
    
    /**
     * 期初库存(厂外)
     */
    @ApiModelProperty(value = "期初库存(厂外)")
    @FieldInterpretation(value = "期初库存(厂外)")
    private Integer openingInventory;
    
    /**
     * 主机厂期初库存
     */
    @ApiModelProperty(value = "主机厂期初库存")
    @FieldInterpretation(value = "主机厂期初库存")
    private Integer oemOpeningInventory;
    
    /**
     * 在途
     */
    @ApiModelProperty(value = "在途")
    @FieldInterpretation(value = "在途")
    private Integer receive;
    
    /**
     * 成品库库存
     */
    @ApiModelProperty(value = "成品库库存")
    @FieldInterpretation(value = "成品库库存")
    private BigDecimal finishedInventory;
    
    
    /**
     * 当月预测
     */
    @ApiModelProperty(value = "当月预测")
    @FieldInterpretation(value = "当月预测")
    private BigDecimal currMonthForecast;
    
    /**
     * 当月已发/当月待发
     */
    @ApiModelProperty(value = "当月已发")
    @FieldInterpretation(value = "当月已发")
    private String currMonthDelivery;
    
    
    /**
     * 客户需求
     */
    @ApiModelProperty(value = "客户需求")
    @FieldInterpretation(value = "客户需求")
    private List<DeliveryPlanCustomerDemandVO> customerDemandList;

    @Override
    public void clean() {

    }

}
