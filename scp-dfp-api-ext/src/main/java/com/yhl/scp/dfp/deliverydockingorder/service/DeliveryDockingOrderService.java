package com.yhl.scp.dfp.deliverydockingorder.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.grp.GrpEdiDeliveryDetail;
import com.yhl.scp.dfp.deliverydockingorder.dto.DeliveryDockingOrderApiDTO;
import com.yhl.scp.dfp.deliverydockingorder.dto.DeliveryDockingOrderDTO;
import com.yhl.scp.dfp.deliverydockingorder.dto.DeliveryDockingOrderExcelDTO;
import com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryDockingOrderDetailVO;
import com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryDockingOrderVO;
import com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryReturnVO;
import com.yhl.scp.dfp.utils.LabelValueFour;
import com.yhl.scp.dfp.utils.LabelValueThree;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

/**
 * <code>DeliveryDockingOrderService</code>
 * <p>
 * 发货对接单管理应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 10:34:33
 */
public interface DeliveryDockingOrderService extends BaseService<DeliveryDockingOrderDTO, DeliveryDockingOrderVO> {

    /**
     * 查询所有
     *
     * @return list {@link DeliveryDockingOrderVO}
     */
    List<DeliveryDockingOrderVO> selectAll();

    /**
     * 自动生成发货对接单号
     *
     * @return
     */
    String generateDeliveryDockingNumber();

    /**
     * 获取物料信息
     *
     * @return
     */
    List<LabelValue<String>> getNewProductStockPoint(List<String> productCodeList);

    /**
     * 获取原始需求版本号
     *
     * @return
     */
    List<String> getOriginDemandVersionCode();

    /**
     * 获取运输路径编码
     *
     * @return
     */
    List<LabelValue<String>> getRoutingCode();

    /**
     * 获取运输方式
     *
     * @return
     */
    List<String> getTransportMode();

    /**
     * 根据versionCode和oemCode获取发货计划中ProductCode
     *
     * @param oemCode
     * @param versionCode
     * @return
     */
    List<String> getProductCodeByOemCodeAndVresionCode(String oemCode, String versionCode);

    /**
     * 获取子库存
     *
     * @return
     */
    List<LabelValue<String>> getSubInventory(String oemCode);

    /**
     * 获取货位
     *
     * @return
     */
    List<LabelValue<String>> getLocation(String stashCode);

    /**
     * 获取供应/贸易类型
     *
     * @return
     */
    List<LabelValue<String>> getBusinessAndMarketType(String oemCode);

    /**
     * 获取子信息详情
     *
     * @param deliveryDockingNumber
     * @return
     */
    List<DeliveryDockingOrderDetailVO> selectDockingOrderDetail(String deliveryDockingNumber);

    /**
     * 获取发货计划版本号
     *
     * @return
     */
    List<String> getDeliveryPlanVersionCode();

    /**
     * 根据versionCode和oemCode获取装车需求ProductCode
     *
     * @param oemCode
     * @param versionCode
     * @return
     */
    List<String> getProductCodeByOemCodeAndVersionCodeFromLoadDemand(String oemCode, String versionCode);

    BaseResponse<Void> sync();

    BaseResponse<Void> doChange(String status);

    List<String> dropdown();

    List<String> getStatusByDeliveryDockingNumber(String deliveryDockingNumber);

    List<LabelValue<String>> getCustomerAddress(String oemCode);

    List<LabelValue<String>> getLineRoutingCode();

    String getTransportModelByLineCode(String routingDetailCode);

    String getBoxTypeByProductCode(String productCode);

    String getBoxTypeByProductCodeId(String productStockPointId);

    List<DeliveryReturnVO> getProductCodeByOemCodeEdi(String oemCode, String selectTime, String currentDayFlag);

    List<String> getVehicleLength();

    List<LabelValue<String>> getProductCodeByOemCode(String oemCode);

    BaseResponse<String> isPublish(String code, Boolean flag, List<DeliveryDockingOrderDetailVO> detailVOS);

    void doCheckData(DeliveryDockingOrderVO mainVo, List<DeliveryDockingOrderDetailVO> deliveryDockingOrderDetailVOS, String code);

    List<DeliveryDockingOrderDetailVO> getDeliveryDetailData(String code);

    DeliveryDockingOrderVO getDeliveryDockingData(String code);

    BaseResponse<Void> updateDeliveryDockingStatus(List<GrpEdiDeliveryDetail> list);

    BaseResponse<Void> doDockingOrderFeedback(DeliveryDockingOrderApiDTO deliveryDockingOrderApiDTO);

    List<LabelValueFour<String>> carrierDataDropDown();

    List<LabelValueThree<String>> getConveyanceByCode(String id);

    List<LabelValue<String>> getTransitClauseByCode(String oemCode);


    DeliveryDockingOrderVO getMainDetailByDockingNumber(String deliveryDockingNumber);

    BaseResponse<Void> updateMesDeliveryDockingStatus(Map<String, Object> map);

    BaseResponse<Void> updateAndSync(DeliveryDockingOrderDTO deliveryDockingOrderDTO);

	void exportTemplate(HttpServletResponse response) throws Exception;

	int importExcel(List<DeliveryDockingOrderExcelDTO> deliveryDockingOrderList, String deliveryDockingNumber);

	Map<String,List<LabelValueThree<String>>> getBoxTypeDropDown(String productCode);

}
