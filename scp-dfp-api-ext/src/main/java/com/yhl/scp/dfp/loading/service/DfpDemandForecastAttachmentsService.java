package com.yhl.scp.dfp.loading.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.loading.dto.DfpDemandForecastAttachmentsDTO;
import com.yhl.scp.dfp.loading.vo.DfpDemandForecastAttachmentsVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <code>DfpDemandForecastAttachmentsService</code>
 * <p>
 * 源文件管理应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-03 14:43:13
 */
public interface DfpDemandForecastAttachmentsService extends BaseService<DfpDemandForecastAttachmentsDTO, DfpDemandForecastAttachmentsVO> {

    /**
     * 查询所有
     *
     * @return list {@link DfpDemandForecastAttachmentsVO}
     */
    List<DfpDemandForecastAttachmentsVO> selectAll();

    void upload(String versionCode, String uploadStatus, Map<String,List<MultipartFile>> fileMap);

    void uploadFile(String versionCode, String oemCode, String uploadStatus, MultipartFile file);

    void download(String id, HttpServletResponse res);

    void remove(String id);

    List<String> listObjects();

}
