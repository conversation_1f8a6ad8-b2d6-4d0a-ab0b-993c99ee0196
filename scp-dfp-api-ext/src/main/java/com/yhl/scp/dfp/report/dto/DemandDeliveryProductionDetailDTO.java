package com.yhl.scp.dfp.report.dto;

import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "需求发货生产报表数据DTO")
@Data
public class DemandDeliveryProductionDetailDTO  implements Serializable {
    private static final long serialVersionUID=1L;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @FieldInterpretation(value = "物料编码")
    private String productCode;
    /**
     * 内部车型
     */
    @ApiModelProperty(value = "内部车型")
    @FieldInterpretation(value = "内部车型")
    private String vehicleModelCode;
}
