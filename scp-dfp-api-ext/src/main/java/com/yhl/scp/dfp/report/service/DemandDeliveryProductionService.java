package com.yhl.scp.dfp.report.service;

import com.yhl.scp.dfp.report.dto.DemandDeliveryProductionDetailDTO;
import com.yhl.scp.dfp.report.vo.DemandDeliveryProductionVO;

import java.util.List;

/**
 * <code>DemandDeliveryProductionService</code>
 * <p>
 * 需求发货生产报表服务接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-15 10:49:11
 */
public interface DemandDeliveryProductionService {

    /**
     * 查询需求发货生产报表数据（原方法，保持兼容性）
     * @param dto 查询条件
     * @return 报表数据列表
     */
    List<DemandDeliveryProductionVO> queryDemandDeliveryProductionReport(DemandDeliveryProductionDetailDTO dto);

    /**
     * 全量查询需求发货生产报表数据（取消分页）
     * @param dto 查询条件
     * @return 全量报表数据
     */
    List<DemandDeliveryProductionVO> queryDemandDeliveryProductionReportWithoutPagination(DemandDeliveryProductionDetailDTO dto);
}
