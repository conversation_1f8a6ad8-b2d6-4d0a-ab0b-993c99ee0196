package com.yhl.scp.dfp.deliverydockingorder.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>DeliveryDockingOrderDTO</code>
 * <p>
 * 发货对接单管理DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 10:34:33
 */
@ApiModel(value = "发货对接单管理DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DeliveryDockingOrderDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -10071103928382317L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 发货对接单号
     */
    @ApiModelProperty(value = "发货对接单号")
    private String deliveryDockingNumber;
    /**
     * mes理货单号
     */
    @ApiModelProperty(value = "mes理货单号")
    private String mesTallyNumber;
    /**
     * 主机厂地址
     */
    @ApiModelProperty(value = "主机厂地址")
    private String oemAddress;
    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址")
    private String harvestAddress;
    /**
     * 中转库（子库存）
     */
    @ApiModelProperty(value = "中转库（子库存）")
    private String transferWarehouse;
    /**
     * 客户（库位）
     */
    @ApiModelProperty(value = "客户（库位）")
    private String customer;
    /**
     * 线路编码
     */
    @ApiModelProperty(value = "线路编码")
    private String lineCode;
    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String customerCode;
    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    private String oemName;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    private String oemCode;
    /**
     * 客户地址
     */
    @ApiModelProperty(value = "客户地址")
    private String customerAddress;
    /**
     * 运输方式
     */
    @ApiModelProperty(value = "运输方式")
    private String transportMode;
    /**
     * 柜型
     */
    @ApiModelProperty(value = "柜型")
    private String cabinetType;
    /**
     * 柜号
     */
    @ApiModelProperty(value = "柜号")
    private String containerNumber;
    /**
     * 车长
     */
    @ApiModelProperty(value = "车长")
    private String vehicleLength;
    /**
     * 发运编码
     */
    @ApiModelProperty(value = "发运编码")
    private String deliveryTransportCode;
    /**
     * 预计到货时间
     */
    @ApiModelProperty(value = "预计到货时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date expectedArriveTime;
    /**
     * 出门条编码
     */
    @ApiModelProperty(value = "出门条编码")
    private String exitBarCode;
    /**
     * mes理货单状态
     */
    @ApiModelProperty(value = "mes理货单状态")
    private String mesTallySheetStatus;
    /**
     * 运输方向
     */
    @ApiModelProperty(value = "运输方向")
    private String transportDirection;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;
    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date deliveryTime;
    /**
     * 超额费用报告
     */
    @ApiModelProperty(value = "超额费用报告")
    private String overageReport;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String versionCode;
    /**
     * 供应类型
     */
    @ApiModelProperty(value = "供应类型")
    private String businessType;
    /**
     * 贸易类型
     */
    @ApiModelProperty(value = "贸易类型")
    private String marketType;
    /**
     * kid
     */
    @ApiModelProperty(value = "kid")
    private String kid;
    /**
     * 数据来源id
     */
    @ApiModelProperty(value = "数据来源id")
    private String dataSourcesId;
    /**
     * 数据源
     */
    @ApiModelProperty(value = "数据源")
    private String dataSources;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 提单号
     */
    @ApiModelProperty(value = "提单号")
    private String sthAuthNo;
    /**
     * 船东
     */
    @ApiModelProperty(value = "船东")
    private String sthRouteNo;
    /**
     * 海关跟踪号
     */
    @ApiModelProperty(value = "海关跟踪号")
    private String mrnNo;
    /**
     * 收货联系人
     */
    @ApiModelProperty(value = "收货联系人")
    private String contact;
    /**
     * 收货联系人电话
     */
    @ApiModelProperty(value = "收货联系人电话")
    private String contactPhone;
    /**
     * pus号
     */
    @ApiModelProperty(value = "pus号")
    private String stdContract;
    /**
     * 承运人ID
     */
    @ApiModelProperty(value = "承运人ID")
    private String sthCarrId;
    /**
     * 运输条款
     */
    @ApiModelProperty(value = "运输条款")
    private String sthFreightTerms;
    /**
     * 运输工具编码
     */
    @ApiModelProperty(value = "运输工具编码")
    private String sthEquipNo;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
    /**
     * 实际发货日期
     */
    @ApiModelProperty(value = "实际发货日期")
    private Date actualArriveTime;

    /**
     * 车辆数量
     */
    @ApiModelProperty(value = "车辆数量")
    private Integer vehicleNumber;
    
    /**
     * 车辆信息
     */
    @ApiModelProperty(value = "车辆信息")
    private String vehicleInfo;
}
