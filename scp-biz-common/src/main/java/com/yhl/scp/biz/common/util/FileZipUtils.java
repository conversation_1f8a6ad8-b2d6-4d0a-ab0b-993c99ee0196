package com.yhl.scp.biz.common.util;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.IOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.zip.Deflater;

/**
 * <code>FileZipUtils</code>
 * <p>
 * 压缩文件下载工具类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-03 17:06:36
 */
@Slf4j
public class FileZipUtils {


    /**
     * 压缩文件下载
     *
     * @param directoryPath 要打压缩包下载得路径（/usr/local/dfp/workspace/97b0910f-0197-bf6c7bba-8aaa85c1-0280）
     * @param zipName       下载压缩包文件名（97b0910f-0197-bf6c7bba-8aaa85c1-0280）
     * @param checkBasePath 检测安全路径（/usr/local/mps/workspace，/usr/local/ams/workspace）
     * @param fileTypes     指定下载路径内得文件类型（txt,xlsx,csv,log....）
     * @return ResponseEntity {@link ResponseEntity}
     */
    public static ResponseEntity<Object> downloadZip(String directoryPath,
                                                     String zipName,
                                                     List<String> checkBasePath,
                                                     List<String> fileTypes) {
        log.info("directoryPath：{}，zipName：{}", directoryPath, zipName);
        // 验证参数不为空
        if (directoryPath == null || zipName == null || checkBasePath == null) {
            return ResponseEntity.badRequest().build();
        }

        // 防止目录遍历攻击：检查路径是否包含../或./
        if (directoryPath.contains("../") || directoryPath.contains("..\\") || directoryPath.contains(".")) {
            log.warn("非法路径包含目录遍历字符: {}", directoryPath);
            throw new BusinessException("非法路径包含目录遍历字符: " + directoryPath);
        }

        // 规范化用户输入路径
        Path inputPath = Paths.get(directoryPath).normalize();

        // 严格验证用户输入：确保只能指向允许访问的文件目录
        boolean isAllowedPath = checkBasePath.stream().anyMatch(basePath -> {
            try {
                Path allowedBasePath = Paths.get(basePath).normalize();
                Path resolvedPath = inputPath.toAbsolutePath();
                return resolvedPath.startsWith(allowedBasePath);
            } catch (Exception e) {
                return false;
            }
        });

        if (!isAllowedPath) {
            throw new BusinessException("非法访问路径: " + directoryPath + "不在允许的基础路径列表中");
        }

        // 白名单机制：只允许下载特定目录下的文件
        if (!isValidDirectory(inputPath)) {
            throw new BusinessException("非法访问目录: " + directoryPath + "目录不存在或不可读");
        }

        // 压缩后的文件路径
        String zipFilePath = "/usr/local/zipFile";
        String outputZipPath = zipFilePath + "/" + zipName + ".zip";
        Path outputPath = Paths.get(outputZipPath).normalize();
        if (!IOUtils.exists(zipFilePath)) {
            log.info("压缩文件路径不存在，执行创建：{}", outputZipPath);
            IOUtils.create(zipFilePath);
        }
        try {
            File zipFile = zipFiles(inputPath, outputPath,fileTypes);
            InputStream inputStream = new FileInputStream(zipFile);
            log.info("压缩文件完成：{}", outputZipPath);
            File file = new File(outputZipPath);
            if (!file.exists()) {
                return ResponseEntity.notFound().build();
            }
            Resource resource = new FileSystemResource(file);
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
        } catch (Exception e) {
            log.error("下载文件失败：{0}", e);
            return ResponseEntity.notFound().build();
        }
    }


    private static boolean isValidDirectory(Path path) {
        try {
            // 验证目录是否有效且位于允许的路径下
            return Files.exists(path) && Files.isDirectory(path) && Files.isReadable(path);
        } catch (Exception e) {
            return false;
        }
    }

    private static File zipFiles(Path inputPath, Path outputZipPath) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(outputZipPath.toFile());
             ZipArchiveOutputStream zipOut = new ZipArchiveOutputStream(fos)) {
            zipOut.setLevel(Deflater.BEST_COMPRESSION);
            Files.walk(inputPath)
                    .filter(path -> !Files.isDirectory(path))
                    .forEach(path -> {
                        ZipArchiveEntry zipEntry =
                                new ZipArchiveEntry(inputPath.relativize(path).toString());
                        try {
                            zipOut.putArchiveEntry(zipEntry);
                            Files.copy(path, zipOut);
                            zipOut.closeArchiveEntry();
                        } catch (IOException e) {
                            throw new RuntimeException("Failed to add file to zip: " + path, e);
                        }
                    });
        }
        return outputZipPath.toFile();
    }

    private static File zipFiles(Path inputPath, Path outputZipPath, List<String> fileTypes) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(outputZipPath.toFile());
             ZipArchiveOutputStream zipOut = new ZipArchiveOutputStream(fos)) {
            zipOut.setLevel(Deflater.BEST_COMPRESSION);
            Files.walk(inputPath)
                    .filter(path -> !Files.isDirectory(path))
                    .filter(path -> {
                        if (fileTypes == null || fileTypes.isEmpty()) {
                            return true;
                        }
                        String fileName = path.getFileName().toString();
                        int dotIndex = fileName.lastIndexOf(".");
                        if (dotIndex == -1 || dotIndex == fileName.length() - 1) {
                            return false;
                        }
                        String fileExtension = fileName.substring(dotIndex + 1).toLowerCase();
                        // 校验压缩目录内得文件扩展后缀是否在指定下载得文件类型内
                        return fileTypes.stream().anyMatch(type -> type.equalsIgnoreCase(fileExtension));
                    })
                    .forEach(path -> {
                        ZipArchiveEntry zipEntry =
                                new ZipArchiveEntry(inputPath.relativize(path).toString());
                        try {
                            zipOut.putArchiveEntry(zipEntry);
                            Files.copy(path, zipOut);
                            zipOut.closeArchiveEntry();
                        } catch (IOException e) {
                            throw new BusinessException("Failed to add file to zip: " + path, e);
                        }
                    });
        }
        return outputZipPath.toFile();
    }

}
