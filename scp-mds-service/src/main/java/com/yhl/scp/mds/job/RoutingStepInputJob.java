package com.yhl.scp.mds.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.bom.service.impl.ProductAboutBomServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * description: 同步bom数据，分别插入bom和bom_version版本中
 * author：李杰
 * email: <EMAIL>
 * date: 2024/11/21
 */
@Component
@Slf4j
public class RoutingStepInputJob {

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private ProductAboutBomServiceImpl service;

    @XxlJob("routingStepInputJob")
    public ReturnT<String> substitutionJob() {
        XxlJobHelper.log("开始同步bom数据");
        List<Scenario> data = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MPS.getCode()).getData();
        if (data.isEmpty()) {
            XxlJobHelper.log("租户不存在该数据");
            return ReturnT.FAIL;
        }
        for (Scenario scenario : data) {
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            try {
                if (!StringUtils.isEmpty(scenario.getTenantId())) {
                    service.syncData(scenario.getTenantId(), null, null, null, scenario.getDataBaseName());
                }
            } catch (Exception e) {
                XxlJobHelper.log("bom定时任务报错{}。", e.getMessage());
                return ReturnT.FAIL;
            }
        }
        XxlJobHelper.log("结束同步bom数据");
        return ReturnT.SUCCESS;
    }


}
