package com.yhl.scp.mds.overdeadlineday.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MdsOverDeadlineDaysDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -29604260805781449L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 组织（库存点)
     */
    private String stockPointCode;
    /**
     * 公司代码
     */
    private String companyCode;
    /**
     * 物料类型
     */
    private String materialsType;
    /**
     * 物料分类大类
     */
    private String materialsMainClassification;
    /**
     * 物料分类小类
     */
    private String materialsSecondClassification;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 颜色代码
     */
    private String colorCode;
    /**
     * 超期界定天数
     */
    private BigDecimal overDeadlineDay;

}
