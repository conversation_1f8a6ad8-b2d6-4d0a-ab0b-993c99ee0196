<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.box.infrastructure.dao.BoxInfoDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.box.infrastructure.po.BoxInfoPO">
        <!--@Table mds_box_info-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="box_type" jdbcType="VARCHAR" property="boxType"/>
        <result column="box_code" jdbcType="VARCHAR" property="boxCode"/>
        <result column="piece_per_box" jdbcType="INTEGER" property="piecePerBox"/>
        <result column="box_per_row" jdbcType="INTEGER" property="boxPerRow"/>
        <result column="per_stack_quantity" jdbcType="INTEGER" property="perStackQuantity"/>
        <result column="box_quantity" jdbcType="INTEGER" property="boxQuantity"/>
        <result column="box_length" jdbcType="VARCHAR" property="boxLength"/>
        <result column="box_width" jdbcType="VARCHAR" property="boxWidth"/>
        <result column="box_height" jdbcType="VARCHAR" property="boxHeight"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="kid" jdbcType="VARCHAR" property="kid"/>
        <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="plan_area" jdbcType="VARCHAR" property="planArea"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.box.vo.BoxInfoVO">
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
    </resultMap>
    <sql id="Base_Column_List">
id,box_type,box_code,piece_per_box,box_per_row,per_stack_quantity,box_quantity,box_length,box_width,box_height,stock_point_id,remark,enabled,creator,create_time,modifier,modify_time,version_value,kid,last_update_time,plan_area
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>, stock_point_code, stock_point_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.boxType != null and params.boxType != ''">
                and box_type = #{params.boxType,jdbcType=VARCHAR}
            </if>
            <if test="params.boxCode != null and params.boxCode != ''">
                and box_code = #{params.boxCode,jdbcType=VARCHAR}
            </if>
            <if test="params.piecePerBox != null">
                and piece_per_box = #{params.piecePerBox,jdbcType=INTEGER}
            </if>
            <if test="params.boxPerRow != null">
                and box_per_row = #{params.boxPerRow,jdbcType=INTEGER}
            </if>
            <if test="params.perStackQuantity != null">
                and per_stack_quantity = #{params.perStackQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.boxQuantity != null">
                and box_quantity = #{params.boxQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.boxLength != null">
                and box_length = #{params.boxLength,jdbcType=VARCHAR}
            </if>
            <if test="params.boxWidth != null">
                and box_width = #{params.boxWidth,jdbcType=VARCHAR}
            </if>
            <if test="params.boxHeight != null">
                and box_height = #{params.boxHeight,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointId != null and params.stockPointId != ''">
                and stock_point_id = #{params.stockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointName != null and params.stockPointName != ''">
                and stock_point_name = #{params.stockPointName,jdbcType=VARCHAR}
            </if>
            <if test="params.kid != null and params.kid != ''">
                and kid = #{params.kid,jdbcType=VARCHAR}
            </if>
            <if test="params.kids != null and params.kids != ''">
                and kid in
                <foreach collection="params.kids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.boxCodes != null and params.boxCodes != ''">
                and box_Code in
                <foreach collection="params.boxCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.lastUpdateTime != null">
                and last_update_time = #{params.lastUpdateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.planArea != null and params.planArea != ''">
                and plan_area = #{params.planArea,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_box_info
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_box_info
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mds_box_info
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_box_info
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mds_box_info
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.box.infrastructure.po.BoxInfoPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_box_info(
        id,
        box_type,
        box_code,
        piece_per_box,
        box_per_row,
        per_stack_quantity,
        box_quantity,
        box_length,
        box_width,
        box_height,
        stock_point_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        kid,
        last_update_time,
        plan_area,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{boxType,jdbcType=VARCHAR},
        #{boxCode,jdbcType=VARCHAR},
        #{piecePerBox,jdbcType=INTEGER},
        #{boxPerRow,jdbcType=INTEGER},
        #{perStackQuantity,jdbcType=INTEGER},
        #{boxQuantity,jdbcType=INTEGER},
        #{boxLength,jdbcType=VARCHAR},
        #{boxWidth,jdbcType=VARCHAR},
        #{boxHeight,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{kid,jdbcType=VARCHAR},
        #{planArea,jdbcType=VARCHAR},
        #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.box.infrastructure.po.BoxInfoPO">
        insert into mds_box_info(
        id,
        box_type,
        box_code,
        piece_per_box,
        box_per_row,
        per_stack_quantity,
        box_quantity,
        box_length,
        box_width,
        box_height,
        box_weight,
        stock_point_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        kid,
        last_update_time,
        plan_area)
        values (
        #{id,jdbcType=VARCHAR},
        #{boxType,jdbcType=VARCHAR},
        #{boxCode,jdbcType=VARCHAR},
        #{piecePerBox,jdbcType=INTEGER},
        #{boxPerRow,jdbcType=INTEGER},
        #{perStackQuantity,jdbcType=INTEGER},
        #{boxQuantity,jdbcType=INTEGER},
        #{boxLength,jdbcType=VARCHAR},
        #{boxWidth,jdbcType=VARCHAR},
        #{boxHeight,jdbcType=VARCHAR},
        #{boxWeight,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{kid,jdbcType=VARCHAR},
        #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{planArea,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_box_info(
        id,
        box_type,
        box_code,
        piece_per_box,
        box_per_row,
        per_stack_quantity,
        box_quantity,
        box_length,
        box_width,
        box_height,
        stock_point_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        kid,
        last_update_time,
        plan_area,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.boxType,jdbcType=VARCHAR},
        #{entity.boxCode,jdbcType=VARCHAR},
        #{entity.piecePerBox,jdbcType=INTEGER},
        #{entity.boxPerRow,jdbcType=INTEGER},
        #{entity.perStackQuantity,jdbcType=INTEGER},
        #{entity.boxQuantity,jdbcType=INTEGER},
        #{entity.boxLength,jdbcType=VARCHAR},
        #{entity.boxWidth,jdbcType=VARCHAR},
        #{entity.boxHeight,jdbcType=VARCHAR},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.kid,jdbcType=VARCHAR},
        #{entity.lastUpdateTime,jdbcType=TIMESTAMP},
        #{entity.planArea,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_box_info(
        id,
        box_type,
        box_code,
        piece_per_box,
        box_per_row,
        per_stack_quantity,
        box_quantity,
        box_length,
        box_width,
        box_height,
        stock_point_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        kid,
        last_update_time,
        plan_area,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.boxType,jdbcType=VARCHAR},
        #{entity.boxCode,jdbcType=VARCHAR},
        #{entity.piecePerBox,jdbcType=INTEGER},
        #{entity.boxPerRow,jdbcType=INTEGER},
        #{entity.perStackQuantity,jdbcType=INTEGER},
        #{entity.boxQuantity,jdbcType=INTEGER},
        #{entity.boxLength,jdbcType=VARCHAR},
        #{entity.boxWidth,jdbcType=VARCHAR},
        #{entity.boxHeight,jdbcType=VARCHAR},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.kid,jdbcType=VARCHAR},
        #{entity.lastUpdateTime,jdbcType=TIMESTAMP},
        #{entity.planArea,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.box.infrastructure.po.BoxInfoPO">
        update mds_box_info set
        box_type = #{boxType,jdbcType=VARCHAR},
        box_code = #{boxCode,jdbcType=VARCHAR},
        piece_per_box = #{piecePerBox,jdbcType=INTEGER},
        box_per_row = #{boxPerRow,jdbcType=INTEGER},
        per_stack_quantity = #{perStackQuantity,jdbcType=INTEGER},
        box_quantity = #{boxQuantity,jdbcType=INTEGER},
        box_length = #{boxLength,jdbcType=VARCHAR},
        box_width = #{boxWidth,jdbcType=VARCHAR},
        box_height = #{boxHeight,jdbcType=VARCHAR},
        stock_point_id = #{stockPointId,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER},
        kid = #{kid,jdbcType=VARCHAR},
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
        plan_area = #{planArea,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.box.infrastructure.po.BoxInfoPO">
        update mds_box_info
        <set>
            <if test="item.boxType != null and item.boxType != ''">
                box_type = #{item.boxType,jdbcType=VARCHAR},
            </if>
            <if test="item.boxCode != null and item.boxCode != ''">
                box_code = #{item.boxCode,jdbcType=VARCHAR},
            </if>
            <if test="item.piecePerBox != null">
                piece_per_box = #{item.piecePerBox,jdbcType=INTEGER},
            </if>
            <if test="item.boxPerRow != null">
                box_per_row = #{item.boxPerRow,jdbcType=INTEGER},
            </if>
            <if test="item.perStackQuantity != null">
                per_stack_quantity = #{item.perStackQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.boxQuantity != null">
                box_quantity = #{item.boxQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.boxLength != null">
                box_length = #{item.boxLength,jdbcType=VARCHAR},
            </if>
            <if test="item.boxWidth != null">
                box_width = #{item.boxWidth,jdbcType=VARCHAR},
            </if>
            <if test="item.boxHeight != null">
                box_height = #{item.boxHeight,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateTime != null">
                last_update_time = #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.planArea != null and item.planArea != ''">
                plan_area = #{item.planArea,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            version_value = version_value + 1,
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_box_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="box_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.boxType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="box_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.boxCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="piece_per_box = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.piecePerBox,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="box_per_row = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.boxPerRow,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="per_stack_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.perStackQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="box_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.boxQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="box_length = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.boxLength,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="box_width = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} and version_value = #{item.versionValue,jdbcType=INTEGER} then
                    #{item.boxWidth,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="box_height = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.boxHeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="kid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.kid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="plan_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mds_box_info
        <set>
            <if test="item.boxType != null and item.boxType != ''">
                box_type = #{item.boxType,jdbcType=VARCHAR},
            </if>
            <if test="item.boxCode != null and item.boxCode != ''">
                box_code = #{item.boxCode,jdbcType=VARCHAR},
            </if>
            <if test="item.piecePerBox != null">
                piece_per_box = #{item.piecePerBox,jdbcType=INTEGER},
            </if>
            <if test="item.boxPerRow != null">
                box_per_row = #{item.boxPerRow,jdbcType=INTEGER},
            </if>
            <if test="item.perStackQuantity != null">
                per_stack_quantity = #{item.perStackQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.boxQuantity != null">
                box_quantity = #{item.boxQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.boxLength != null">
                box_length = #{item.boxLength,jdbcType=VARCHAR},
            </if>
            <if test="item.boxWidth != null">
                box_width = #{item.boxWidth,jdbcType=VARCHAR},
            </if>
            <if test="item.boxHeight != null">
                box_height = #{item.boxHeight,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateTime != null">
                last_update_time = #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.planArea != null and item.planArea != ''">
                plan_area = #{item.planArea,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mds_box_info
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_box_info where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!-- 批量id+版本删除 -->
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from mds_box_info where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>
</mapper>
