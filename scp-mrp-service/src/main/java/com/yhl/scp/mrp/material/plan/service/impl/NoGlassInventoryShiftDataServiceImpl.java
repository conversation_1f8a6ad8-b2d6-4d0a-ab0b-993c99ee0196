package com.yhl.scp.mrp.material.plan.service.impl;

import cn.hutool.core.date.StopWatch;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.PlatformUser;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dfp.risk.enums.RiskLevelEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierPurchaseRatioVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.assignment.dto.MaterialDemandAssignmentDTO;
import com.yhl.scp.mrp.assignment.service.MaterialDemandAssignmentService;
import com.yhl.scp.mrp.enums.DemandPatternEnum;
import com.yhl.scp.mrp.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.enums.PlanNeedPublishStatusEnum;
import com.yhl.scp.mrp.extension.material.vo.MaterialPlanNeedVO;
import com.yhl.scp.mrp.inventory.dto.MaterialPlanInventoryShiftParamDTO;
import com.yhl.scp.mrp.material.forecast.service.MaterialLongTermForecastService;
import com.yhl.scp.mrp.material.plan.convertor.MaterialPlanNeedConvertor;
import com.yhl.scp.mrp.material.plan.convertor.NoGlassInventoryShiftDataConvertor;
import com.yhl.scp.mrp.material.plan.domain.entity.NoGlassInventoryShiftDataDO;
import com.yhl.scp.mrp.material.plan.domain.service.NoGlassInventoryShiftDataDomainService;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanNeedDTO;
import com.yhl.scp.mrp.material.plan.dto.NoGlassInventoryShiftDataDTO;
import com.yhl.scp.mrp.material.plan.enums.MaterialTypeEnum;
import com.yhl.scp.mrp.material.plan.enums.MrpDemandSourceEnum;
import com.yhl.scp.mrp.material.plan.enums.MrpSupplySourceEnum;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.NoGlassInventoryShiftDataDao;
import com.yhl.scp.mrp.material.plan.infrastructure.po.NoGlassInventoryShiftDataPO;
import com.yhl.scp.mrp.material.plan.service.*;
import com.yhl.scp.mrp.material.plan.vo.*;
import com.yhl.scp.mrp.material.purchase.dto.SyncMaterialPurchaseDTO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequirementDetailService;
import com.yhl.scp.mrp.order.service.PurchaseOrderInfoService;
import com.yhl.scp.mrp.order.vo.PurchaseOrderInfoVO;
import com.yhl.scp.mrp.published.dto.MaterialPlanPublishedVersionDTO;
import com.yhl.scp.mrp.published.infrastructure.dao.NoGlassInventoryShiftDataPublishedDao;
import com.yhl.scp.mrp.published.infrastructure.dao.NoGlassInventoryShiftDemandPublishedDao;
import com.yhl.scp.mrp.published.infrastructure.dao.NoGlassInventoryShiftDetailPublishedDao;
import com.yhl.scp.mrp.published.infrastructure.dao.NoGlassInventoryShiftSupplyPublishedDao;
import com.yhl.scp.mrp.published.service.MaterialPlanPublishedVersionService;
import com.yhl.scp.mrp.selfSufficient.service.MaterialSelfSufficientRequirementDetailService;
import com.yhl.scp.mrp.supplier.service.MaterialSupplierPurchaseService;
import com.yhl.scp.mrp.supplier.service.SupplierPurchaseRatioService;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>NoGlassInventoryShiftDataServiceImpl</code>
 * <p>
 * 非原片库存推移主表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-26 15:25:04
 */
@Slf4j
@Service
public class NoGlassInventoryShiftDataServiceImpl extends AbstractService implements NoGlassInventoryShiftDataService {

    @Resource
    private NoGlassInventoryShiftDataDao noGlassInventoryShiftDataDao;

    @Resource
    private NoGlassInventoryShiftDataDomainService noGlassInventoryShiftDataDomainService;

    @Resource
    private NoGlassInventoryShiftDetailService noGlassInventoryShiftDetailService;

    @Resource
    private MaterialPlanInventoryShiftDemandService materialPlanInventoryShiftDemandService;

    @Resource
    private MaterialPlanInventoryShiftSupplyService materialPlanInventoryShiftSupplyService;

    @Resource
    private MaterialSupplierPurchaseService materialSupplierPurchaseService;

    @Resource
    private SupplierPurchaseRatioService supplierPurchaseRatioService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private PurchaseOrderInfoService purchaseOrderInfoService;

    @Resource
    private MaterialPlanNeedService materialPlanNeedService;

    @Resource
    private MaterialPurchaseRequirementDetailService materialPurchaseRequirementDetailService;

    @Resource
    private MaterialDemandAssignmentService materialDemandAssignmentService;

    @Resource
    private MaterialPlanPublishedVersionService materialPlanPublishedVersionService;

    @Resource
    private NoGlassInventoryShiftDemandPublishedDao noGlassInventoryShiftDemandPublishedDao;

    @Resource
    private NoGlassInventoryShiftSupplyPublishedDao noGlassInventoryShiftSupplyPublishedDao;

    @Resource
    private NoGlassInventoryShiftDataPublishedDao noGlassInventoryShiftDataPublishedDao;

    @Resource
    private NoGlassInventoryShiftDetailPublishedDao noGlassInventoryShiftDetailPublishedDao;

    @Resource
    private MaterialSelfSufficientRequirementDetailService materialSelfSufficientRequirementDetailService;

    @Resource
    private MaterialLongTermForecastService materialLongTermForecastService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private SpringBeanUtils springBeanUtils;

    private static MaterialInformationVO getMaterialInformationVO(NoGlassInventoryShiftDataVO shiftDataVO,
                                                                  MaterialSupplierPurchaseVO materialSupplierPurchaseVO,
                                                                  Map<String, NewProductStockPointVO> productStockPointVOMapOfCode,
                                                                  Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup,
                                                                  Map<String, SupplierVO> supplierVOMapOfId) {
        MaterialInformationVO materialInformationVO = new MaterialInformationVO();
        materialInformationVO.setMaterialCode(shiftDataVO.getProductCode());
        materialInformationVO.setMaterialType(shiftDataVO.getProductCategory());
        materialInformationVO.setSafetyStockDaysMin(shiftDataVO.getSafetyStockDaysMin());
        materialInformationVO.setSafetyStockDaysStandard(shiftDataVO.getSafetyStockDaysStandard());
        materialInformationVO.setSafetyStockDaysMax(shiftDataVO.getSafetyStockDaysMax());
        if (StringUtils.isNotEmpty(shiftDataVO.getMaterialRiskLevel())) {
            materialInformationVO.setMaterialRiskLevel(RiskLevelEnum.getDescByCode(shiftDataVO.getMaterialRiskLevel()));
        }
        materialInformationVO.setVehicleModelCode(shiftDataVO.getVehicleModeCode());

        if (null != materialSupplierPurchaseVO) {
            materialInformationVO.setMaterialProperty(materialSupplierPurchaseVO.getMaterialProperty());
            materialInformationVO.setMinOrderQty(materialSupplierPurchaseVO.getMinOrderQty());
            materialInformationVO.setPurchaseLot(materialSupplierPurchaseVO.getPurchaseLot());
            materialInformationVO.setPackageLot(materialSupplierPurchaseVO.getPackageLot());
            materialInformationVO.setOrderPlacementLeadTimeDay(materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay());
            materialInformationVO.setRequestCargoPlanLockDay(materialSupplierPurchaseVO.getRequestCargoPlanLockDay());
            // 要货模式
            materialInformationVO.setDemandPattern(materialSupplierPurchaseVO.getDemandPattern());
            // 供应商名称
            // 获取供应商采购比例数据
            List<SupplierPurchaseRatioVO> supplierPurchaseRatioVOList = supplierPurchaseRatioVOGroup.get(materialSupplierPurchaseVO.getId());
            if (CollectionUtils.isNotEmpty(supplierPurchaseRatioVOList)) {
                StringBuilder supplierName = new StringBuilder();
                StringBuilder frequencyDelivery = new StringBuilder();
                for (SupplierPurchaseRatioVO supplierPurchaseRatioVO : supplierPurchaseRatioVOList) {
                    SupplierVO supplierVO = supplierVOMapOfId.get(supplierPurchaseRatioVO.getSupplierId());
                    if (null != supplierVO) {
                        supplierName.append(supplierVO.getSupplierName()).append("|");
                        if (StringUtils.isNotBlank(supplierVO.getReceivingWeekDay())) {
                            frequencyDelivery.append("星期:").append(supplierVO.getReceivingWeekDay()).append("|");
                        }
                        if (StringUtils.isNotBlank(supplierVO.getReceivingMonthDay())) {
                            frequencyDelivery.append("每月收货:").append(supplierVO.getReceivingMonthDay()).append("|");
                        }
                    }
                }
                // 去除最后一个竖线
                if (supplierName.length() > 0) {
                    materialInformationVO.setSupplierName(supplierName.substring(0, supplierName.length() - 1));
                }
                if (frequencyDelivery.length() > 0) {
                    materialInformationVO.setFrequencyDelivery(frequencyDelivery.substring(0, frequencyDelivery.length() - 1));
                }
            }
        }
        if (productStockPointVOMapOfCode.containsKey(shiftDataVO.getProductCode())) {
            materialInformationVO.setMaterialName(productStockPointVOMapOfCode.get(shiftDataVO.getProductCode()).getProductName());
        }
        return materialInformationVO;
    }

    public static String convertDayOfWeekToChinese(int dayOfWeek) {
        Map<Integer, String> dayMap = new HashMap<>();
        dayMap.put(1, "一");
        dayMap.put(2, "二");
        dayMap.put(3, "三");
        dayMap.put(4, "四");
        dayMap.put(5, "五");
        dayMap.put(6, "六");
        dayMap.put(7, "日");
        return dayMap.getOrDefault(dayOfWeek, "未知");
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(NoGlassInventoryShiftDataDTO noGlassInventoryShiftDataDTO) {
        // 0.数据转换
        NoGlassInventoryShiftDataDO noGlassInventoryShiftDataDO = NoGlassInventoryShiftDataConvertor.INSTANCE.dto2Do(noGlassInventoryShiftDataDTO);
        NoGlassInventoryShiftDataPO noGlassInventoryShiftDataPO = NoGlassInventoryShiftDataConvertor.INSTANCE.dto2Po(noGlassInventoryShiftDataDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        noGlassInventoryShiftDataDomainService.validation(noGlassInventoryShiftDataDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(noGlassInventoryShiftDataPO);
        noGlassInventoryShiftDataDao.insert(noGlassInventoryShiftDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(NoGlassInventoryShiftDataDTO noGlassInventoryShiftDataDTO) {
        // 0.数据转换
        NoGlassInventoryShiftDataDO noGlassInventoryShiftDataDO = NoGlassInventoryShiftDataConvertor.INSTANCE.dto2Do(noGlassInventoryShiftDataDTO);
        NoGlassInventoryShiftDataPO noGlassInventoryShiftDataPO = NoGlassInventoryShiftDataConvertor.INSTANCE.dto2Po(noGlassInventoryShiftDataDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        noGlassInventoryShiftDataDomainService.validation(noGlassInventoryShiftDataDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(noGlassInventoryShiftDataPO);
        noGlassInventoryShiftDataDao.update(noGlassInventoryShiftDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<NoGlassInventoryShiftDataDTO> list) {
        List<NoGlassInventoryShiftDataPO> newList = NoGlassInventoryShiftDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        noGlassInventoryShiftDataDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<NoGlassInventoryShiftDataDTO> list) {
        List<NoGlassInventoryShiftDataPO> newList = NoGlassInventoryShiftDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        noGlassInventoryShiftDataDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return noGlassInventoryShiftDataDao.deleteBatch(idList);
        }
        return noGlassInventoryShiftDataDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public NoGlassInventoryShiftDataVO selectByPrimaryKey(String id) {
        NoGlassInventoryShiftDataPO po = noGlassInventoryShiftDataDao.selectByPrimaryKey(id);
        return NoGlassInventoryShiftDataConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "NO_GLASS_INVENTORY_SHIFT_DATA")
    public List<NoGlassInventoryShiftDataVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "NO_GLASS_INVENTORY_SHIFT_DATA")
    public List<NoGlassInventoryShiftDataVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<NoGlassInventoryShiftDataVO> dataList = noGlassInventoryShiftDataDao.selectByCondition(sortParam, queryCriteriaParam);
        NoGlassInventoryShiftDataServiceImpl target = SpringBeanUtils.getBean(NoGlassInventoryShiftDataServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<NoGlassInventoryShiftDataVO> selectByParams(Map<String, Object> params) {
        List<NoGlassInventoryShiftDataPO> list = noGlassInventoryShiftDataDao.selectByParams(params);
        return NoGlassInventoryShiftDataConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<NoGlassInventoryShiftDataVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.NO_GLASS_INVENTORY_SHIFT_DATA.getCode();
    }

    @Override
    public List<NoGlassInventoryShiftDataVO> invocation(List<NoGlassInventoryShiftDataVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    @Expression("v_mrp_no_glass_inventory_shift_data")
    public List<MaterialPlanInventoryShiftPageVO> selectPage2(MaterialPlanInventoryShiftParamDTO paramDTO) {
        StopWatch stopWatch = new StopWatch("非原片材料推移查询");
        stopWatch.start("查询其他组装数据");
        List<MaterialPlanInventoryShiftPageVO> result = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();

        //根据供应商ID查询关联的材料
        List<String> supplierMaterialCodeList = null;
        if (StringUtils.isNotBlank(paramDTO.getSupplierId())) {
            // 查询供应商分配比例
            List<SupplierPurchaseRatioVO> supplierPurchaseRatioVOList = supplierPurchaseRatioService.selectByParams(ImmutableMap.of("supplierId", paramDTO.getSupplierId()));
            if (CollectionUtils.isEmpty(supplierPurchaseRatioVOList)) {
                return new ArrayList<>();
            }
            List<String> supplierProductIds = supplierPurchaseRatioVOList.stream().map(SupplierPurchaseRatioVO::getSupplierProductId).distinct().collect(Collectors.toList());
            // 查询供应商与材料的关系
            List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOS = materialSupplierPurchaseService.selectByParams(ImmutableMap.of("ids", supplierProductIds));
            if (CollectionUtils.isEmpty(materialSupplierPurchaseVOS)) {
                return new ArrayList<>();
            }
            supplierMaterialCodeList = materialSupplierPurchaseVOS.stream().map(MaterialSupplierPurchaseVO::getMaterialCode).distinct().collect(Collectors.toList());
        }
        params.put("productCodes", supplierMaterialCodeList);

        List<String> productIdList = new ArrayList<>();
        // 根据本厂编码查询原材料
        if (StringUtils.isNotBlank(paramDTO.getFactoryCode())) {
            // 查询本厂编码ID
            List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointVOByProductCodes(SystemHolder.getScenario(), Lists.newArrayList(paramDTO.getFactoryCode()));
            if (CollectionUtils.isNotEmpty(newProductStockPointVOS)) {
                productIdList.addAll(newProductStockPointVOS.stream().map(NewProductStockPointVO::getId).collect(Collectors.toList()));
            }
        }

        // 获取关联车型的本厂编码
        if (StringUtils.isNotBlank(paramDTO.getVehicleModelCode())) {
            List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(null, ImmutableMap.of("vehicleModeCode", paramDTO.getVehicleModelCode()));
            if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
                return new ArrayList<>();
            }
            productIdList.addAll(newProductStockPointVOS.stream().map(NewProductStockPointVO::getId).distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(productIdList)) {
            // 进行去重
            productIdList = productIdList.stream().distinct().collect(Collectors.toList());
            // 查询BOM
            List<ProductBomVersionVO> productBomVersionVOList = newMdsFeign.selectProductBomVersionVOByParams(SystemHolder.getScenario(), ImmutableMap.of("productIds", productIdList));
            if (CollectionUtils.isEmpty(productBomVersionVOList)) {
                return new ArrayList<>();
            }
            List<String> bomVersionIds = productBomVersionVOList.stream().map(ProductBomVersionVO::getId).distinct().collect(Collectors.toList());
            List<ProductBomVO> productBomVOList = newMdsFeign.selectProductBomVOByParams(SystemHolder.getScenario(), ImmutableMap.of("bomVersionIds", bomVersionIds));
            if (CollectionUtils.isEmpty(productBomVOList)) {
                return new ArrayList<>();
            }
            List<String> ioProductCodeList = productBomVOList.stream().map(ProductBomVO::getIoProductCode).distinct().collect(Collectors.toList());
            // supplierMaterialCodeList和ioProductCodeList取交集
            if (supplierMaterialCodeList == null) {
                params.put("productCodes", ioProductCodeList);
            } else {
                List<String> interSection = com.yhl.platform.common.utils.CollectionUtils.getInterSection(ioProductCodeList, supplierMaterialCodeList);
                params.put("productCodes", interSection);
            }
        }

        String materialCode = paramDTO.getMaterialCode();
        if (StringUtils.isNotBlank(materialCode)) {
            // 字母默认转大写，去除掉空格
            materialCode = materialCode.toUpperCase().replaceAll(" ", "");
        }
        stopWatch.stop();
        stopWatch.start("查询推移分页数据");

//        List<Role> roleList = ipsNewFeign.selectRoleListByUserId(SystemHolder.getUserId());
//        List<String> roleNameList = roleList.stream().map(Role::getRoleName).distinct().collect(Collectors.toList());
//        // 含有材料计划员、PVB计划员则需要加查询条件
//        if (roleNameList.contains("材料计划员") || roleNameList.contains("B/PVB计划员")) {
//            params.put("userId", SystemHolder.getUserId());
//        }
        params.put("productCategory", paramDTO.getMaterialType());
        params.put("productCode", materialCode);
//        params.put("inventoryDateStart", paramDTO.getStartDate());
//        params.put("inventoryDateEnd", paramDTO.getEndDate());
        // 分组查询材料的信息
        PageHelper.startPage(paramDTO.getPageNum(), paramDTO.getPageSize());
        List<NoGlassInventoryShiftDataVO> shiftDataVOList;

        // 通过条件区分明细查询（提升响应速度）
        shiftDataVOList = this.selectBySpecialNotDetailParams(params);
        PageInfo<NoGlassInventoryShiftDataVO> pageInfo = new PageInfo<>(shiftDataVOList);
        stopWatch.stop();
        stopWatch.start("查询推移数据");
        if (CollectionUtils.isNotEmpty(shiftDataVOList)) {
            List<String> dataIdList = shiftDataVOList.stream().map(NoGlassInventoryShiftDataVO::getId).collect(Collectors.toList());
            List<String> productCodeList = shiftDataVOList.stream().map(NoGlassInventoryShiftDataVO::getProductCode).collect(Collectors.toList());

            Map<String, Object> detailParams = new HashMap<>();
            detailParams.put("noGlassInventoryShiftDataIdList", dataIdList);
            detailParams.put("inventoryDateStart", paramDTO.getStartDate());
            detailParams.put("inventoryDateEnd", paramDTO.getEndDate());
            detailParams.put("warningRemind", paramDTO.getWarningRemind());
            detailParams.put("beforeWarningRemind", paramDTO.getBeforeWarningRemind());
            List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftVOS = noGlassInventoryShiftDetailService.selectVOByParams(detailParams);
            if (CollectionUtils.isEmpty(noGlassInventoryShiftVOS)) {
                return new ArrayList<>();
            }

            // 查询锁定期开始时间
            Date lockPeriodStartDate = noGlassInventoryShiftVOS.stream().min(Comparator.comparing(NoGlassInventoryShiftDetailVO::getInventoryDate)).get().getInventoryDate();

            // 查询材料与供应商关系数据
            List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOS = materialSupplierPurchaseService.selectByParams(ImmutableMap.of("materialCodeList", productCodeList));
            // 查询供应商比例数据
            List<String> supplierProductIds = materialSupplierPurchaseVOS.stream().map(MaterialSupplierPurchaseVO::getId).collect(Collectors.toList());
            List<SupplierPurchaseRatioVO> supplierPurchaseRatioVOList = supplierPurchaseRatioService.selectByParams(ImmutableMap.of("supplierProductIds", supplierProductIds));
            Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup = supplierPurchaseRatioVOList.stream().collect(Collectors.groupingBy(SupplierPurchaseRatioVO::getSupplierProductId));
            // 获取供应商数据
            List<String> supplierIdList = supplierPurchaseRatioVOList.stream().map(SupplierPurchaseRatioVO::getSupplierId).distinct().collect(Collectors.toList());
            List<SupplierVO> supplierVOS = newMdsFeign.selectSupplierByParams(SystemHolder.getScenario(), ImmutableMap.of("ids", supplierIdList));
            Map<String, SupplierVO> supplierVOMapOfId = supplierVOS.stream().collect(Collectors.toMap(SupplierVO::getId, Function.identity(), (k1, k2) -> k2));

            // 组装noGlassInventoryShiftPageVO
            stopWatch.stop();
            stopWatch.start("组装推移数据");
            result = assembleMaterialPlanInventoryShiftPageVO(shiftDataVOList, noGlassInventoryShiftVOS, materialSupplierPurchaseVOS,
                    supplierPurchaseRatioVOGroup, supplierVOMapOfId, paramDTO, lockPeriodStartDate);
        }
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
        // 根据物料排序
        result.sort(Comparator.comparing(vo -> vo.getMaterialInformationVO().getMaterialCode()));
        result.forEach(data -> data.setPageInfo(pageInfo));
        return result;
    }

    private List<MaterialPlanInventoryShiftPageVO> assembleMaterialPlanInventoryShiftPageVO(List<NoGlassInventoryShiftDataVO> shiftDataVOList,
                                                                                            List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftVOS,
                                                                                            List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOS,
                                                                                            Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup,
                                                                                            Map<String, SupplierVO> supplierVOMapOfId,
                                                                                            MaterialPlanInventoryShiftParamDTO materialPlanInventoryShiftParamDTO,
                                                                                            Date lockPeriodStartDate) {
        List<MaterialPlanInventoryShiftPageVO> result = new ArrayList<>();

        // 查询库存点物品
        List<String> productCodeList = shiftDataVOList.stream().map(NoGlassInventoryShiftDataVO::getProductCode).collect(Collectors.toList());
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByProducts(productCodeList);
        Map<String, NewProductStockPointVO> productStockPointVOMapOfCode = newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (k1, k2) -> k2));

        List<String> idList = noGlassInventoryShiftVOS.stream().map(NoGlassInventoryShiftDetailVO::getId).collect(Collectors.toList());
        // 查询毛需求
        List<MaterialPlanInventoryShiftDemandVO> noGlassInventoryShiftDemandVOList = materialPlanInventoryShiftDemandService.selectByMaterialPlanInventoryShiftIds(idList);
        Map<String, List<MaterialPlanInventoryShiftDemandVO>> noGlassInventoryShiftDemandVOGroup = noGlassInventoryShiftDemandVOList.stream().collect(Collectors.groupingBy(MaterialPlanInventoryShiftDemandVO::getMaterialPlanInventoryShiftId));
        // 查询供应
        List<MaterialPlanInventoryShiftSupplyVO> noGlassInventoryShiftSupplyVOList = materialPlanInventoryShiftSupplyService.selectByMaterialPlanInventoryShiftIds(idList);
        Map<String, List<MaterialPlanInventoryShiftSupplyVO>> noGlassInventoryShiftSupplyVOGroup = noGlassInventoryShiftSupplyVOList.stream().collect(Collectors.groupingBy(MaterialPlanInventoryShiftSupplyVO::getMaterialPlanInventoryShiftId));

        Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMap = materialSupplierPurchaseVOS.stream().collect(Collectors.toMap(MaterialSupplierPurchaseVO::getMaterialCode, Function.identity(), (k1, k2) -> k2));
        Map<String, List<NoGlassInventoryShiftDetailVO>> collect = noGlassInventoryShiftVOS.stream().collect(Collectors.groupingBy(NoGlassInventoryShiftDetailVO::getNoGlassInventoryShiftDataId));

        List<Date> intervalDates;
        if (null != materialPlanInventoryShiftParamDTO.getStartDate() && null != materialPlanInventoryShiftParamDTO.getEndDate()) {
            intervalDates = DateUtils.getIntervalDates(materialPlanInventoryShiftParamDTO.getStartDate(), materialPlanInventoryShiftParamDTO.getEndDate());
        } else {
            Date moveDate = DateUtils.moveMonth(materialPlanInventoryShiftParamDTO.getMaterialPlanVersionCreateTime(), 12);
            intervalDates = DateUtils.getIntervalDates(materialPlanInventoryShiftParamDTO.getMaterialPlanVersionCreateTime(), moveDate);
        }

        for (NoGlassInventoryShiftDataVO shiftDataVO : shiftDataVOList) {
            // 前30天按照日期、往后日期按照每7天汇总
            List<NoGlassInventoryShiftDetailVO> shiftDetailVOList = collect.get(shiftDataVO.getId());
            List<NoGlassInventoryShiftDetailVO> list = new ArrayList<>();
            Map<String, NoGlassInventoryShiftDetailVO> noGlassInventoryShiftMapOfDateStr = new HashMap<>();
            if (CollectionUtils.isNotEmpty(shiftDetailVOList)) {
                noGlassInventoryShiftMapOfDateStr = shiftDetailVOList.stream().collect(Collectors.toMap(item -> DateUtils.dateToString(item.getInventoryDate(), DateUtils.COMMON_DATE_STR3), Function.identity(), (v1, v2) -> v1));
            }

            List<Date> firstPartList;
            List<Date> secondPartList = new ArrayList<>();

            if (intervalDates.size() > 30) {
                firstPartList = intervalDates.subList(0, 29);
                secondPartList = intervalDates.subList(29, intervalDates.size() - 1);
            } else {
                firstPartList = intervalDates;
            }
            firstPartList.sort(Comparator.comparing(Date::getTime));

            MaterialPlanInventoryShiftPageVO materialPlanInventoryShiftPageVO = new MaterialPlanInventoryShiftPageVO();
            MaterialSupplierPurchaseVO materialSupplierPurchaseVO = supplierPurchaseVOMap.get(shiftDataVO.getProductCode());
            // 获取锁定期
            int requestCargoPlanLockDay = 0;
            if (null != materialSupplierPurchaseVO) {
                if (null != materialSupplierPurchaseVO.getRequestCargoPlanLockDay() && StringUtils.equals(DemandPatternEnum.PLAN_NEED.getCode(), materialSupplierPurchaseVO.getDemandPattern())) {
                    requestCargoPlanLockDay = materialSupplierPurchaseVO.getRequestCargoPlanLockDay().intValue();
                }
                if (null != materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay() && StringUtils.equals(DemandPatternEnum.PO.getCode(), materialSupplierPurchaseVO.getDemandPattern())) {
                    requestCargoPlanLockDay = materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay().intValue();
                }
            }
            // 获取锁定期的差值
            Long dateInterval = DateUtils.getDateInterval(lockPeriodStartDate, firstPartList.get(0));
            requestCargoPlanLockDay = requestCargoPlanLockDay - dateInterval.intValue();


            // 组装材料信息
            MaterialInformationVO materialInformationVO = getMaterialInformationVO(shiftDataVO, materialSupplierPurchaseVO,
                    productStockPointVOMapOfCode, supplierPurchaseRatioVOGroup, supplierVOMapOfId);

            // 前30天需求
            for (int i = 0; i < firstPartList.size(); i++) {
                boolean whetherDisplayedColor = false;
                Date firstPartDate = firstPartList.get(i);
                String weekToChinese = convertDayOfWeekToChinese(DateUtils.getDayOfWeek(firstPartDate));
                String dateStr = DateUtils.dateToString(firstPartDate, DateUtils.COMMON_DATE_STR3);
                NoGlassInventoryShiftDetailVO shiftDetailVO = new NoGlassInventoryShiftDetailVO();
                if (i < requestCargoPlanLockDay) {
                    whetherDisplayedColor = true;
                }
                if (noGlassInventoryShiftMapOfDateStr.containsKey(dateStr)) {
                    shiftDetailVO = noGlassInventoryShiftMapOfDateStr.get(dateStr);
                    shiftDetailVO.setWhetherDisplayedColor(whetherDisplayedColor);
                    List<MaterialPlanInventoryShiftDemandVO> inventoryShiftDemandVOS = noGlassInventoryShiftDemandVOGroup.get(shiftDetailVO.getId());
                    List<MaterialPlanInventoryShiftSupplyVO> inventoryShiftSupplyVOS = noGlassInventoryShiftSupplyVOGroup.get(shiftDetailVO.getId());
                    // 组装需求
                    if (CollectionUtils.isNotEmpty(inventoryShiftDemandVOS)) {
                        assembleMrpDemandShiftDemandList(shiftDetailVO, inventoryShiftDemandVOS);
                    }
                    // 组装供应
                    if (CollectionUtils.isNotEmpty(inventoryShiftSupplyVOS)) {
                        assembleMrpSupplyShiftSupplyList(shiftDetailVO, inventoryShiftSupplyVOS);
                    }
                }
                shiftDetailVO.setInventoryDateDimension(dateStr + "(" + weekToChinese + ")");
                list.add(shiftDetailVO);
            }

            // 汇总周需求(合并7天数据)
            if (CollectionUtils.isNotEmpty(secondPartList)) {
                secondPartList.sort(Comparator.comparing(Date::getTime));
                List<List<Date>> partition = Lists.partition(secondPartList, 7);
                for (List<Date> dateList : partition) {
                    List<NoGlassInventoryShiftDetailVO> mergeShiftDetailVOList = new ArrayList<>();
                    for (Date date : dateList) {
                        String dateStr = DateUtils.dateToString(date, DateUtils.COMMON_DATE_STR3);
                        NoGlassInventoryShiftDetailVO noGlassInventoryShiftVO = noGlassInventoryShiftMapOfDateStr.get(dateStr);
                        if (null != noGlassInventoryShiftVO) {
                            mergeShiftDetailVOList.add(noGlassInventoryShiftVO);
                        }
                    }

                    if (CollectionUtils.isEmpty(mergeShiftDetailVOList)) continue;
                    // 期初库存为第一天的期初
                    BigDecimal openingInventory = mergeShiftDetailVOList.get(0).getOpeningInventory();
                    // 期末库存为最后一天的期末
                    BigDecimal endingInventory = mergeShiftDetailVOList.get(mergeShiftDetailVOList.size() - 1).getEndingInventory();
                    BigDecimal beforeEndingInventory = mergeShiftDetailVOList.get(mergeShiftDetailVOList.size() - 1).getBeforeEndingInventory();
                    BigDecimal safetyStockLevelMin = mergeShiftDetailVOList.get(0).getSafetyStockLevelMin();
                    BigDecimal safetyStockLevelStandard = mergeShiftDetailVOList.get(0).getSafetyStockLevelStandard();
                    BigDecimal safetyStockLevelMax = mergeShiftDetailVOList.get(0).getSafetyStockLevelMax();

                    NoGlassInventoryShiftDetailVO noGlassInventoryShiftDetailVO = new NoGlassInventoryShiftDetailVO();
                    BigDecimal demandMcbQuantitySumMcb = BigDecimal.ZERO;
                    BigDecimal demandMcbQuantitySumMps = BigDecimal.ZERO;
                    BigDecimal demandMcbQuantitySumZzk = BigDecimal.ZERO;
                    BigDecimal planPurchaseSupplyQuantitySum = BigDecimal.ZERO;
                    BigDecimal transitOrderSupplyQuantitySum = BigDecimal.ZERO;

                    List<MaterialPlanInventoryShiftDemandVO> inventoryShiftDemandMcbAllList = new ArrayList<>();
                    List<MaterialPlanInventoryShiftDemandVO> inventoryShiftDemandMpsAllList = new ArrayList<>();
                    List<MaterialPlanInventoryShiftDemandVO> inventoryShiftDemandZzkAllList = new ArrayList<>();
                    List<MaterialPlanInventoryShiftSupplyVO> planPurchaseInventoryShiftSupplyAllList = new ArrayList<>();
                    List<MaterialPlanInventoryShiftSupplyVO> transitOrderInventoryShiftSupplyAllList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(mergeShiftDetailVOList)) {
                        noGlassInventoryShiftDetailVO = mergeShiftDetailVOList.stream().sorted(Comparator.comparing(NoGlassInventoryShiftDetailVO::getInventoryDate)).collect(Collectors.toList()).get(0);
                        // 汇总需求
                        BigDecimal totalDemandQty = mergeShiftDetailVOList.stream().map(NoGlassInventoryShiftDetailVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        // 汇总计划调整量
                        BigDecimal totalPlanAdjustmentQty = mergeShiftDetailVOList.stream().map(NoGlassInventoryShiftDetailVO::getAdjustQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                        // 汇总计划采购
                        BigDecimal totalPlanPurchaseQty = mergeShiftDetailVOList.stream().map(NoGlassInventoryShiftDetailVO::getPlanPurchase).reduce(BigDecimal.ZERO, BigDecimal::add);
                        // 汇总供应量
                        BigDecimal totalSupplyQty = mergeShiftDetailVOList.stream().map(NoGlassInventoryShiftDetailVO::getSupplyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);

                        noGlassInventoryShiftDetailVO.setDemandQuantity(totalDemandQty);
                        noGlassInventoryShiftDetailVO.setAdjustQuantity(totalPlanAdjustmentQty);
                        noGlassInventoryShiftDetailVO.setSupplyQuantity(totalSupplyQty);
                        // 计划采购
                        noGlassInventoryShiftDetailVO.setPlanPurchase(totalPlanPurchaseQty);

                        for (NoGlassInventoryShiftDetailVO mergeShiftDetailVO : mergeShiftDetailVOList) {
                            List<MaterialPlanInventoryShiftDemandVO> inventoryShiftDemandVOS = noGlassInventoryShiftDemandVOGroup.get(mergeShiftDetailVO.getId());
                            List<MaterialPlanInventoryShiftSupplyVO> inventoryShiftSupplyVOS = noGlassInventoryShiftSupplyVOGroup.get(mergeShiftDetailVO.getId());
                            // 组装需求
                            if (CollectionUtils.isNotEmpty(inventoryShiftDemandVOS)) {
                                assembleMrpDemandShiftDemandList(mergeShiftDetailVO, inventoryShiftDemandVOS);
                                if (CollectionUtils.isNotEmpty(mergeShiftDetailVO.getForecastDemandList())) {
                                    inventoryShiftDemandMcbAllList.addAll(mergeShiftDetailVO.getForecastDemandList());
                                    demandMcbQuantitySumMcb = demandMcbQuantitySumMcb.add(mergeShiftDetailVO.getForecastDemandList().stream().map(MaterialPlanInventoryShiftDemandVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                                }
                                if (CollectionUtils.isNotEmpty(mergeShiftDetailVO.getProductionDemandList())) {
                                    inventoryShiftDemandMpsAllList.addAll(mergeShiftDetailVO.getProductionDemandList());
                                    demandMcbQuantitySumMps = demandMcbQuantitySumMps.add(mergeShiftDetailVO.getProductionDemandList().stream().map(MaterialPlanInventoryShiftDemandVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                                }
                                if (CollectionUtils.isNotEmpty(mergeShiftDetailVO.getZzkDemandList())) {
                                    inventoryShiftDemandZzkAllList.addAll(mergeShiftDetailVO.getZzkDemandList());
                                    demandMcbQuantitySumZzk = demandMcbQuantitySumZzk.add(mergeShiftDetailVO.getZzkDemandList().stream().map(MaterialPlanInventoryShiftDemandVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                                }
                            }

                            // 组装供应
                            if (CollectionUtils.isNotEmpty(inventoryShiftSupplyVOS)) {
                                assembleMrpSupplyShiftSupplyList(mergeShiftDetailVO, inventoryShiftSupplyVOS);
                                if (CollectionUtils.isNotEmpty(mergeShiftDetailVO.getPlanPurchaseInventoryShiftSupplyList())) {
                                    planPurchaseInventoryShiftSupplyAllList.addAll(mergeShiftDetailVO.getPlanPurchaseInventoryShiftSupplyList());
                                    planPurchaseSupplyQuantitySum = planPurchaseSupplyQuantitySum.add(mergeShiftDetailVO.getPlanPurchaseInventoryShiftSupplyList().stream().map(MaterialPlanInventoryShiftSupplyVO::getSupplyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                                }
                                if (CollectionUtils.isNotEmpty(mergeShiftDetailVO.getTransitOrderInventoryShiftSupplyList())) {
                                    transitOrderInventoryShiftSupplyAllList.addAll(mergeShiftDetailVO.getTransitOrderInventoryShiftSupplyList());
                                    transitOrderSupplyQuantitySum = transitOrderSupplyQuantitySum.add(mergeShiftDetailVO.getTransitOrderInventoryShiftSupplyList().stream().map(MaterialPlanInventoryShiftSupplyVO::getSupplyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                                }
                            }
                        }
                    }

                    noGlassInventoryShiftDetailVO.setOpeningInventory(openingInventory);
                    noGlassInventoryShiftDetailVO.setEndingInventory(endingInventory);
                    noGlassInventoryShiftDetailVO.setBeforeEndingInventory(beforeEndingInventory);
                    noGlassInventoryShiftDetailVO.setSafetyStockLevelMin(safetyStockLevelMin);
                    noGlassInventoryShiftDetailVO.setSafetyStockLevelStandard(safetyStockLevelStandard);
                    noGlassInventoryShiftDetailVO.setSafetyStockLevelMax(safetyStockLevelMax);

                    noGlassInventoryShiftDetailVO.setForecastDemandQuantity(demandMcbQuantitySumMcb);
                    noGlassInventoryShiftDetailVO.setProductionDemandQuantity(demandMcbQuantitySumMps);
                    noGlassInventoryShiftDetailVO.setZzkDemandQuantity(demandMcbQuantitySumZzk);
                    noGlassInventoryShiftDetailVO.setForecastDemandList(inventoryShiftDemandMcbAllList);
                    noGlassInventoryShiftDetailVO.setProductionDemandList(inventoryShiftDemandMpsAllList);
                    noGlassInventoryShiftDetailVO.setZzkDemandList(inventoryShiftDemandZzkAllList);

                    noGlassInventoryShiftDetailVO.setPlanPurchaseSupplyQuantity(planPurchaseSupplyQuantitySum);
                    noGlassInventoryShiftDetailVO.setTransitOrderSupplyQuantity(transitOrderSupplyQuantitySum);
                    noGlassInventoryShiftDetailVO.setPlanPurchaseInventoryShiftSupplyList(planPurchaseInventoryShiftSupplyAllList);
                    noGlassInventoryShiftDetailVO.setTransitOrderInventoryShiftSupplyList(transitOrderInventoryShiftSupplyAllList);
                    String startDateStr = DateUtils.dateToString(dateList.get(0), DateUtils.COMMON_DATE_STR3);
                    String endDateStr = DateUtils.dateToString(dateList.get(dateList.size() - 1), DateUtils.COMMON_DATE_STR3);
                    noGlassInventoryShiftDetailVO.setInventoryDateDimension(String.join("~", startDateStr, endDateStr));
                    // 汇总供应
                    list.add(noGlassInventoryShiftDetailVO);
                }
            }
            materialPlanInventoryShiftPageVO.setNoGlassnventoryShiftList(list);
            materialPlanInventoryShiftPageVO.setMaterialInformationVO(materialInformationVO);
            result.add(materialPlanInventoryShiftPageVO);
        }
        return result;
    }

    private void assembleMrpDemandShiftDemandList(NoGlassInventoryShiftDetailVO noGlassInventoryShiftVO, List<MaterialPlanInventoryShiftDemandVO> inventoryShiftDemandVOS) {
        // 组装需求
        Map<String, List<MaterialPlanInventoryShiftDemandVO>> mrpDemandGroupOfType = inventoryShiftDemandVOS.stream().collect(Collectors.groupingBy(MaterialPlanInventoryShiftDemandVO::getDemandType));
        List<MaterialPlanInventoryShiftDemandVO> inventoryShiftDemandMcbList = mrpDemandGroupOfType.get(MrpDemandSourceEnum.MCB.getCode());
        List<MaterialPlanInventoryShiftDemandVO> inventoryShiftDemandMpsList = mrpDemandGroupOfType.get(MrpDemandSourceEnum.MPS.getCode());
        List<MaterialPlanInventoryShiftDemandVO> inventoryShiftDemandZzkList = mrpDemandGroupOfType.get(MrpDemandSourceEnum.ZZK.getCode());

        if (CollectionUtils.isNotEmpty(inventoryShiftDemandMcbList)) {
            noGlassInventoryShiftVO.setForecastDemandList(inventoryShiftDemandMcbList);
            noGlassInventoryShiftVO.setForecastDemandQuantity(inventoryShiftDemandMcbList.stream().map(MaterialPlanInventoryShiftDemandVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        if (CollectionUtils.isNotEmpty(inventoryShiftDemandMpsList)) {
            noGlassInventoryShiftVO.setProductionDemandList(inventoryShiftDemandMpsList);
            noGlassInventoryShiftVO.setProductionDemandQuantity(inventoryShiftDemandMpsList.stream().map(MaterialPlanInventoryShiftDemandVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        if (CollectionUtils.isNotEmpty(inventoryShiftDemandZzkList)) {
            noGlassInventoryShiftVO.setZzkDemandList(inventoryShiftDemandZzkList);
            noGlassInventoryShiftVO.setZzkDemandQuantity(inventoryShiftDemandZzkList.stream().map(MaterialPlanInventoryShiftDemandVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
    }

    private void assembleMrpSupplyShiftSupplyList(NoGlassInventoryShiftDetailVO noGlassInventoryShiftVO, List<MaterialPlanInventoryShiftSupplyVO> inventoryShiftSupplyVOS) {
        Map<String, List<MaterialPlanInventoryShiftSupplyVO>> mrpSupplyGroupOfType = inventoryShiftSupplyVOS.stream().collect(Collectors.groupingBy(MaterialPlanInventoryShiftSupplyVO::getSupplyType));
        List<MaterialPlanInventoryShiftSupplyVO> planPurchaseInventoryShiftSupplyVOS = mrpSupplyGroupOfType.get(MrpSupplySourceEnum.PLAN_PURCHASE_INVENTORY.getCode());
        List<MaterialPlanInventoryShiftSupplyVO> transitOrderInventoryShiftSupplyVOS = mrpSupplyGroupOfType.get(MrpSupplySourceEnum.TRANSIT_ORDER_INVENTORY.getCode());

        BigDecimal supplyQuantitySum = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(planPurchaseInventoryShiftSupplyVOS)) {
            noGlassInventoryShiftVO.setPlanPurchaseSupplyQuantity(planPurchaseInventoryShiftSupplyVOS.stream().map(MaterialPlanInventoryShiftSupplyVO::getSupplyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
            noGlassInventoryShiftVO.setPlanPurchaseInventoryShiftSupplyList(planPurchaseInventoryShiftSupplyVOS);
            supplyQuantitySum = supplyQuantitySum.add(noGlassInventoryShiftVO.getPlanPurchaseSupplyQuantity());
        }

        if (CollectionUtils.isNotEmpty(transitOrderInventoryShiftSupplyVOS)) {
            noGlassInventoryShiftVO.setTransitOrderSupplyQuantity(transitOrderInventoryShiftSupplyVOS.stream().map(MaterialPlanInventoryShiftSupplyVO::getSupplyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
            noGlassInventoryShiftVO.setTransitOrderInventoryShiftSupplyList(transitOrderInventoryShiftSupplyVOS);
            supplyQuantitySum = supplyQuantitySum.add(noGlassInventoryShiftVO.getTransitOrderSupplyQuantity());
        }
        noGlassInventoryShiftVO.setSupplyQuantity(supplyQuantitySum);
    }

    @Override
    public List<NoGlassInventoryShiftDataVO> selectBySpecialParams(Map<String, Object> params) {
        return noGlassInventoryShiftDataDao.selectBySpecialParams(params);
    }

    private List<NoGlassInventoryShiftDataVO> selectBySpecialNotDetailParams(Map<String, Object> params) {
        return noGlassInventoryShiftDataDao.selectBySpecialNotDetailParams(params);
    }

    @Override
    public Set<String> doPublish(List<String> productCodeList) {
        StopWatch stopWatch = new StopWatch("材料推移结果发布");
        Set<String> errorMsgList = new HashSet<>();

        stopWatch.start("查询材料计划员物料权限");
        // 查询计划员的物料权限
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("product_code"))
                .queryParam(ImmutableMap.of("materialPlanner", SystemHolder.getUserId()))
                .build();
        List<NewProductStockPointVO> newProductStockPointVOList =
                newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);
        List<String> permissionCodeList = newProductStockPointVOList.stream()
                .map(NewProductStockPointVO::getProductCode)
                .distinct()
                .collect(Collectors.toList());
        stopWatch.stop();
        if (CollectionUtils.isEmpty(newProductStockPointVOList)) {
            throw new BusinessException("用户没有物料权限");
        }

        if (CollectionUtils.isEmpty(productCodeList)) {
            productCodeList = permissionCodeList;
        }

        List<NoGlassInventoryShiftDataVO> shiftDataList = this.selectByParams(ImmutableMap.of("productCodeList", productCodeList));
        List<String> dataIdList = shiftDataList.stream().map(NoGlassInventoryShiftDataVO::getId).collect(Collectors.toList());
        List<String> materialCodeList = shiftDataList.stream().map(NoGlassInventoryShiftDataVO::getProductCode).distinct().collect(Collectors.toList());

        stopWatch.start("查询推移结果数据");
        log.info("+++++++++++++++++++++++ 开始查询推移结果数据");
        // 根据材料计划版本ID查询推移数据
        List<NoGlassInventoryShiftDetailVO> planInventoryShiftVOS = new ArrayList<>(1024 * 1024);
        Lists.partition(dataIdList, 1000).forEach(item -> planInventoryShiftVOS.addAll(
                noGlassInventoryShiftDetailService.selectByParams(ImmutableMap.of("noGlassInventoryShiftDataIdList", item))));
        if (CollectionUtils.isEmpty(planInventoryShiftVOS)) {
            throw new BusinessException("没有库存推移数据");
        }
        stopWatch.stop();
        log.info("+++++++++++++++++++++++ 推移结果数据查询完成");
        stopWatch.start("查询其他数据");

        List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOS = materialSupplierPurchaseService
                .selectByParams(ImmutableMap.of("materialCodeList", materialCodeList));
        // 查询材料采购
        if (CollectionUtils.isEmpty(materialSupplierPurchaseVOS)) {
            throw new BusinessException("没有材料与供应商关系数据");
        }
        List<String> materialSupplierPurchaseIdList = materialSupplierPurchaseVOS.stream().map(MaterialSupplierPurchaseVO::getId).collect(Collectors.toList());

        List<SupplierPurchaseRatioVO> purchaseRatioVOList = supplierPurchaseRatioService.
                selectByParams(ImmutableMap.of("supplierProductIds", materialSupplierPurchaseIdList));
        if (null == purchaseRatioVOList) {
            purchaseRatioVOList = new ArrayList<>();
        }

        Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup = purchaseRatioVOList.stream()
                .collect(Collectors.groupingBy(SupplierPurchaseRatioVO::getSupplierProductId));

        // 获取供应商数据
        List<SupplierVO> supplierVOS = newMdsFeign.selectSupplierByParams(SystemHolder.getScenario(), new HashMap<>());

        Map<String, SupplierVO> supplierVOMapOfId = supplierVOS.stream()
                .collect(Collectors.toMap(SupplierVO::getId, Function.identity(), (k1, k2) -> k2));

        Map<String, SupplierVO> supplierVOMapOfCode = supplierVOS.stream()
                .collect(Collectors.toMap(SupplierVO::getSupplierCode, Function.identity(), (k1, k2) -> k2));

        // 进行分组
        Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMap = materialSupplierPurchaseVOS.stream()
                .collect(Collectors.toMap(MaterialSupplierPurchaseVO::getMaterialCode, Function.identity(), (k1, k2) -> k2));

        // 查询采购PO
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("productCodes", materialCodeList);
        List<PurchaseOrderInfoVO> purchaseOrderInfoVOList = purchaseOrderInfoService.selectByParams(paramsMap);
        Date nowDate = new Date();
        // 要货计划模式
        List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftListOfPlanNeed = new ArrayList<>();
        // PO模式
        List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftListOfPo = new ArrayList<>();
        // 自给件模式
        List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftListOfCs = new ArrayList<>();
        // 中长期预测PO模式
        List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftListOfForecastPo = new ArrayList<>();
        // 库存推移数据，根据材料编码进行分组
        List<NoGlassInventoryShiftDetailVO> filterNoGlassInventoryShiftList = planInventoryShiftVOS.stream()
                .filter(item -> item.getPlanPurchase().compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.toList());
        stopWatch.stop();

        stopWatch.start("进行发布处理,要货计划、采购需求");
        // 获取最新发布版本号
        String versionCode = materialPlanPublishedVersionService.getVersionCode(MaterialTypeEnum.NO_GLASS.getCode());

        Map<String, List<NoGlassInventoryShiftDetailVO>> noGlassDetailGroupOfDataId = filterNoGlassInventoryShiftList.stream()
                .collect(Collectors.groupingBy(NoGlassInventoryShiftDetailVO::getNoGlassInventoryShiftDataId));

        assemblePublishedListData(shiftDataList, noGlassDetailGroupOfDataId, supplierPurchaseVOMap, supplierPurchaseRatioVOGroup, nowDate,
                noGlassInventoryShiftListOfForecastPo, noGlassInventoryShiftListOfPo, noGlassInventoryShiftListOfPlanNeed,
                noGlassInventoryShiftListOfCs);
        stopWatch.stop();

        // 同步要货计划
        if (CollectionUtils.isNotEmpty(noGlassInventoryShiftListOfPlanNeed)) {
            materialPlanNeedService.doSyncPlanNeedData(shiftDataList, noGlassInventoryShiftListOfPlanNeed,
                    supplierPurchaseVOMap, supplierPurchaseRatioVOGroup, supplierVOMapOfId, versionCode,permissionCodeList);
        }

        // 同步材料采购需求
        if (CollectionUtils.isNotEmpty(noGlassInventoryShiftListOfPo)) {
            errorMsgList.addAll(materialPurchaseRequirementDetailService.doSyncMaterialPurchase(shiftDataList, noGlassInventoryShiftListOfPo,
                    purchaseOrderInfoVOList, supplierPurchaseVOMap, supplierPurchaseRatioVOGroup, supplierVOMapOfId, versionCode,permissionCodeList));
        }

        // 同步自给件需求
        if (CollectionUtils.isNotEmpty(noGlassInventoryShiftListOfCs)) {
            materialSelfSufficientRequirementDetailService.doSyncSelfSufficientRequirementDetail(shiftDataList, noGlassInventoryShiftListOfCs,
                    supplierPurchaseVOMap, supplierPurchaseRatioVOGroup, supplierVOMapOfId);
        }

        // 同步材料中长期PO数据
        // 查询supply数据
        List<MaterialPlanInventoryShiftSupplyVO> inventoryShiftSupplyVOS = materialPlanInventoryShiftSupplyService
                .selectVOByParams(ImmutableMap.of("supplyType", MrpSupplySourceEnum.PLAN_PURCHASE_INVENTORY.getCode()));
        if (CollectionUtils.isNotEmpty(noGlassInventoryShiftListOfForecastPo)) {
            materialLongTermForecastService.doSyncLongTermForecastData(shiftDataList, noGlassInventoryShiftListOfForecastPo,
                    supplierPurchaseVOMap, supplierPurchaseRatioVOGroup, supplierVOMapOfId, supplierVOMapOfCode,
                    DemandPatternEnum.PO.getCode(), inventoryShiftSupplyVOS, versionCode);
        }

        // 同步材料中长期要货计划
        if (CollectionUtils.isNotEmpty(noGlassInventoryShiftListOfPlanNeed)) {
            materialLongTermForecastService.doSyncLongTermForecastData(shiftDataList, noGlassInventoryShiftListOfPlanNeed,
                    supplierPurchaseVOMap, supplierPurchaseRatioVOGroup, supplierVOMapOfId, supplierVOMapOfCode,
                    DemandPatternEnum.PLAN_NEED.getCode(), inventoryShiftSupplyVOS, versionCode);
        }

        stopWatch.start("持久化发布数据");
        if (CollectionUtils.isNotEmpty(noGlassInventoryShiftListOfPlanNeed)
                || CollectionUtils.isNotEmpty(noGlassInventoryShiftListOfPo)
                || CollectionUtils.isNotEmpty(noGlassInventoryShiftListOfCs)
        ) {
            List<String> finalProductCodeList = productCodeList;
            Executor executor = Executors.newFixedThreadPool(1);
            // 创建异步任务
            CompletableFuture.runAsync(() -> {
                try {
                    saveNoGlassPublishedData(versionCode, finalProductCodeList, dataIdList);
                } catch (Exception e) {
                    log.error("材料MRP持久化发布数据异常", e);
                    throw new BusinessException("材料MRP持久化发布数据异常", e);
                }
            }, executor);
        }
        stopWatch.stop();
        log.info("材料发布耗时：{}", stopWatch.prettyPrint(TimeUnit.SECONDS));
        return errorMsgList;
    }

    private void assemblePublishedListData(List<NoGlassInventoryShiftDataVO> shiftDataList, Map<String, List<NoGlassInventoryShiftDetailVO>> noGlassDetailGroupOfDataId,
                                           Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMap,
                                           Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup,
                                           Date nowDate, List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftListOfForecastPo,
                                           List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftListOfPo,
                                           List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftListOfPlanNeed,
                                           List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftListOfCs) {
        for (NoGlassInventoryShiftDataVO shiftDataVO : shiftDataList) {
            List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftVOList = noGlassDetailGroupOfDataId.get(shiftDataVO.getId());
            MaterialSupplierPurchaseVO materialSupplierPurchaseVO = supplierPurchaseVOMap.get(shiftDataVO.getProductCode());
            if (CollectionUtils.isEmpty(noGlassInventoryShiftVOList)) {
                log.info("材料mrp发布物料:{}, dataId:{}", shiftDataVO.getProductCode(), shiftDataVO.getId());
                continue;
            }
            if (null == materialSupplierPurchaseVO) {
                continue;
            }
            // 要货模式
            String demandPattern = materialSupplierPurchaseVO.getDemandPattern();

            List<SupplierPurchaseRatioVO> supplierPurchaseRatioVOList = supplierPurchaseRatioVOGroup.get(materialSupplierPurchaseVO.getId());
            if (CollectionUtils.isEmpty(supplierPurchaseRatioVOList) && StringUtils.equals(DemandPatternEnum.PLAN_NEED.getCode(), demandPattern)) {
                continue;
            }

            // 订单下达提前期
            int orderPlacementLeadTimeDay = materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay() == null ? 0 : materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay().intValue();

            // 默认为采购PO模式
            if (StringUtils.equals(DemandPatternEnum.PO.getCode(), demandPattern) || StringUtils.isBlank(demandPattern)) {
                // 长期预测数据不需要根据时间过滤
                noGlassInventoryShiftListOfForecastPo.addAll(noGlassInventoryShiftVOList);

                // 数据过滤：要货日期<=当前日期+订单下达提前期的(订单下达提前期需要加+30天)
                Date moveDay = DateUtils.moveDay(nowDate, orderPlacementLeadTimeDay + 30);

                List<NoGlassInventoryShiftDetailVO> filterList = noGlassInventoryShiftVOList.stream()
                        .filter(item -> item.getInventoryDate().compareTo(moveDay) <= 0)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filterList)) {
                    continue;
                }
                noGlassInventoryShiftListOfPo.addAll(filterList);
            }

            if (StringUtils.equals(DemandPatternEnum.PLAN_NEED.getCode(), demandPattern)) {
                //【要货计划】模式,生成要货计划
                // 要货计划生成时，生成所有的，没有时间限制
                noGlassInventoryShiftListOfPlanNeed.addAll(noGlassInventoryShiftVOList);
            }

            if (StringUtils.equals(DemandPatternEnum.CS.getCode(), demandPattern)) {
                //【要货计划】模式,生成要货计划
                // 要货计划生成时，生成所有的，没有时间限制
                noGlassInventoryShiftListOfCs.addAll(noGlassInventoryShiftVOList);
            }
        }
    }

    private void saveNoGlassPublishedData(String versionCode, List<String> productCodeList, List<String> dataIdList) {
        // 新增发布版本
        MaterialPlanPublishedVersionDTO materialPlanPublishedVersionDTO = new MaterialPlanPublishedVersionDTO();
        materialPlanPublishedVersionDTO.setId(UUID.randomUUID().toString());
        materialPlanPublishedVersionDTO.setMaterialType(MaterialTypeEnum.NO_GLASS.getCode());
        materialPlanPublishedVersionDTO.setVersionCode(versionCode);
        if (redisUtil.hasKey(RedisKeyManageEnum.MRP_COMPUTE_GROSS_DEMAND_VERSION_CODE.getKey().replace("{userId}", SystemHolder.getUserId()))) {
            materialPlanPublishedVersionDTO.setGrossDemandVersionCode((String)
                    redisUtil.get(RedisKeyManageEnum.MRP_COMPUTE_GROSS_DEMAND_VERSION_CODE.getKey().replace("{userId}", SystemHolder.getUserId()))
            );
        }
        materialPlanPublishedVersionService.doCreate(materialPlanPublishedVersionDTO);

        // 新增推移数据
        Date date = new Date();
        String userId = SystemHolder.getUserId();
        String uuid = UUIDUtil.getUUID();
        // 存储过程插入data表
        this.doSaveNoGlassDataPublished(uuid, materialPlanPublishedVersionDTO.getId(), date, userId, String.join(",", productCodeList));

        // 存储过程插入detail表
        noGlassInventoryShiftDetailPublishedDao.noGlassDetailPublishedBatch(uuid, date, userId, String.join(",", dataIdList));

        // 存储过程插入noGlassDemand表
        noGlassInventoryShiftDemandPublishedDao.noGlassDemandPublishedBatch(uuid, date, userId, String.join(",", productCodeList));

        // 存储过程插入noGlassSupply表
        noGlassInventoryShiftSupplyPublishedDao.noGlassSupplyPublishedBatch(uuid, date, userId, String.join(",", productCodeList));
    }

    @Override
    public void doSaveNoGlassDataPublished(String uuid, String materialPlanPublishedVersionId, Date date, String userId, String productCodeList) {
        noGlassInventoryShiftDataPublishedDao.saveNoGlassDataPublished(uuid, materialPlanPublishedVersionId, date, userId, productCodeList);
    }

    @Override
    public Date getLastCreateTime(String userId) {
        // 查询该用户的物料权限
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("product_code"))
                .queryParam(ImmutableMap.of("materialPlanner", userId))
                .build();
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);
        if (CollectionUtils.isNotEmpty(newProductStockPointVOS)) {
            List<Date> dateList = new ArrayList<>();
            List<String> productCodeList = newProductStockPointVOS.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
            List<List<String>> partition = Lists.partition(productCodeList, 1000);
            for (List<String> productCodes : partition) {
                Date date = noGlassInventoryShiftDataDao.getLastCreateTime(productCodes);
                if (null != date) {
                    dateList.add(date);
                }
            }
            if (CollectionUtils.isNotEmpty(dateList)) {
                return Collections.max(dateList);
            }
        }
        return null;
    }

}