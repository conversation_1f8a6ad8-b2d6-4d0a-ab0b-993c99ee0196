package com.yhl.scp.mrp.material.plan.service;

import cn.hutool.core.date.StopWatch;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mrp.enums.TransferStatusEnum;
import com.yhl.scp.mrp.freestorage.infrastructure.dao.ProFreeStorageDao;
import com.yhl.scp.mrp.freestorage.infrastructure.po.ProFreeStoragePO;
import com.yhl.scp.mrp.inventory.service.GlassSafetyInventoryService;
import com.yhl.scp.mrp.inventory.service.InventoryAlternativeRelationshipService;
import com.yhl.scp.mrp.inventory.vo.*;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.plan.allocate.InventoryAllocator;
import com.yhl.scp.mrp.material.plan.convertor.GlassInventoryShiftDataConvertor;
import com.yhl.scp.mrp.material.plan.domain.entity.MrpAllocateResultDO;
import com.yhl.scp.mrp.material.plan.dto.*;
import com.yhl.scp.mrp.material.plan.enums.*;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.GlassInventoryShiftDataDao;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.GlassInventoryShiftDetailDao;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanInventoryOccupyDao;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanReplaceDao;
import com.yhl.scp.mrp.material.plan.infrastructure.po.*;
import com.yhl.scp.mrp.material.plan.service.utils.GlassMrpUtil;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryOccupyVO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanTransferInventoryDetailVO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanTransferVO;
import com.yhl.scp.mrp.substitutionRelationship.service.GlassSubstitutionRelationshipService;
import com.yhl.scp.mrp.substitutionRelationship.service.impl.GlassSubstitutionRelationshipServiceImpl;
import com.yhl.scp.mrp.substitutionRelationship.vo.GlassSubstitutionRelationshipVO;
import com.yhl.scp.mrp.transport.service.TransportRoutingService;
import com.yhl.scp.mrp.transport.vo.TransportRoutingVO;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>AbstractGlassMrpService</code>
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-13 22:22:28
 */
public abstract class AbstractGlassMrpService extends AbstractMrpService {

    private static final Logger log = LoggerFactory.getLogger(AbstractGlassMrpService.class);
    @Resource
    protected InventoryAlternativeRelationshipService inventoryAlternativeRelationshipService;
    @Resource
    protected TransportRoutingService transportRoutingService;
    @Resource
    protected MaterialPlanTransferService materialPlanTransferService;
    @Resource
    protected MaterialPlanTransferInventoryDetailService materialPlanTransferInventoryDetailService;
    @Resource
    protected GlassSafetyInventoryService glassSafetyInventoryService;
    @Resource
    protected ProFreeStorageDao proFreeStorageDao;
    @Resource
    protected GlassInventoryShiftDataDao glassInventoryShiftDataDao;
    @Resource
    protected GlassInventoryShiftDetailDao glassInventoryShiftDetailDao;
    @Resource
    protected MaterialPlanInventoryOccupyDao materialPlanInventoryOccupyDao;
    @Resource
    protected MaterialPlanReplaceDao materialPlanReplaceDao;
    @Resource
    protected GlassSubstitutionRelationshipService glassSubstitutionRelationshipService;

    @Override
    protected void getGradeInventoryDemand(MrpContextDTO mrpContextDTO) {

    }

    protected void initMrpSupply(MrpContextDTO mrpContextDTO) {
        // 设置本厂库存供应（本厂现有库存+计划到柜量+在途）
        setFactorySupplyList(mrpContextDTO);
        // 设置港口库存供应
        setPortSupplyList(mrpContextDTO);
        // 设置浮法库存供应
        setFloatSupplyList(mrpContextDTO);
    }

    @Override
    protected void initOtherInfo(MrpContextDTO mrpContextDTO) {
        DynamicDataSourceContextHolder.setDataSource(mrpContextDTO.getScenario());
        // 原片本厂编码车型信息
        initGlassFactoryCode(mrpContextDTO);
        // 设置原片混合替代关系
        setMixSubstitution(mrpContextDTO);
        // 设置调拨计划
        setMaterialPlanTransferList(mrpContextDTO);
        // 设置供应数据
        initMrpSupply(mrpContextDTO);
        // 原片运输路径
        setTransportRouting(mrpContextDTO);
        // 计划替换配置
        setInventoryPlanReplaceData(mrpContextDTO);
        // 设置原片替代映射数据
        setGlassSubstitutionRelationship(mrpContextDTO);
        // 根据混合替代重置物料编码
        resetProductCodeByMixSubstitution(mrpContextDTO);
        // 根据计划替配置数据计算物料的日总需求
        setMaterialDayTotalDemand(mrpContextDTO);
        // 设置免堆期
        setFreeStorage(mrpContextDTO);
        // 设置安全库存管理信息
        initGlassSafetyInventory(mrpContextDTO);
    }

    private void setGlassSubstitutionRelationship(MrpContextDTO mrpContextDTO) {
        // 查询原片替代映射
        List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipVOList = glassSubstitutionRelationshipService.selectByParams(
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));
        mrpContextDTO.setGlassSubstitutionRelationshipList(glassSubstitutionRelationshipVOList);
    }

    private void initGlassFactoryCode(MrpContextDTO mrpContextDTO) {
        List<NewProductStockPointVO> newProductStockPointVOList = mrpNewProductStockPointDao.selectYpFactoryCode();
//        List<NewProductStockPointVO> newProductStockPointVOList = mdsFeign.getYpFactoryCode(mrpContextDTO.getScenario());
        mrpContextDTO.setGlassOfFactoryCodeMap(newProductStockPointVOList.stream().collect(Collectors.groupingBy(NewProductStockPointVO::getProductCode)));
    }

    private void initGlassSafetyInventory(MrpContextDTO mrpContextDTO) {
        List<GlassSafetyInventoryVO> glassSafetyInventoryVOList = glassSafetyInventoryService.selectAll();
        Map<String, GlassSafetyInventoryVO> glassSafetyInventoryMap = glassSafetyInventoryVOList.stream()
                .collect(Collectors.toMap(data -> data.getColor() + "&" + data.getThickness(), Function.identity(), (v1, v2) -> v1));
        mrpContextDTO.setGlassSafetyInventoryMap(glassSafetyInventoryMap);
    }


    private static BigDecimal getReplaceQuantity(InventoryAlternativeRelationshipVO inventoryAlternativeRelationshipVO,
                                                 BigDecimal demandQuantity, boolean useReplace) {
        BigDecimal remainingQuantity = useReplace ? inventoryAlternativeRelationshipVO.getUseReplaceRemainingQuantity()
                : inventoryAlternativeRelationshipVO.getUsedAsReplaceRemainingQuantity();
        BigDecimal replaceQuantityTemp;
        if (remainingQuantity.compareTo(demandQuantity) >= 0) {
            replaceQuantityTemp = demandQuantity;
        } else {
            replaceQuantityTemp = remainingQuantity;
        }
        if (useReplace) {
            inventoryAlternativeRelationshipVO.setUseReplaceRemainingQuantity(remainingQuantity.subtract(replaceQuantityTemp));
        } else {
            inventoryAlternativeRelationshipVO.setUsedAsReplaceRemainingQuantity(remainingQuantity.subtract(replaceQuantityTemp));
        }

        return replaceQuantityTemp;
    }

    private void setTransportRouting(MrpContextDTO mrpContextDTO) {
        Map<String, String> stockPointCodeMap = mrpContextDTO.getStockPointVOList().stream()
                .collect(Collectors.toMap(NewStockPointVO::getId, NewStockPointVO::getStockPointCode));
        List<TransportRoutingVO> transportRoutingList = transportRoutingService.selectAll();
        transportRoutingList.forEach(t -> {
            t.setDestinStockPointCode(stockPointCodeMap.get(t.getDestinStockPointId()));
            t.setOriginStockPointCode(stockPointCodeMap.get(t.getOriginStockPointId()));
        });
        // 运输路径
        Map<String, List<TransportRoutingVO>> transportRoutingGroupMap = transportRoutingList.stream()
                .collect(Collectors.groupingBy(t -> t.getOriginStockPointCode() + "&&" + t.getDestinStockPointCode()));
        mrpContextDTO.setTransportRoutingList(transportRoutingList);
        mrpContextDTO.setTransportRoutingGroupMap(transportRoutingGroupMap);
    }

    protected void resetProductCodeByMixSubstitution(MrpContextDTO mrpContextDTO) {
        // 重置原片替代关系物料编码
        if (CollectionUtils.isNotEmpty(mrpContextDTO.getInventoryAlternativeRelationshipList())) {
            mrpContextDTO.getInventoryAlternativeRelationshipList().forEach(t -> {
                if (!t.getDemandProductCode().contains("*")) {
                    String demandProductCode = GlassMrpUtil.getNewProductCode(t.getDemandProductCode(), mrpContextDTO.getMixSubstitution());
                    t.setDemandProductCode(demandProductCode);
                }
                String replaceProductCode = GlassMrpUtil.getNewProductCode(t.getReplacedProductCode(), mrpContextDTO.getMixSubstitution());
                t.setReplacedProductCode(replaceProductCode);
            });
        }

        // 重置原片需求物料编码
        if (CollectionUtils.isNotEmpty(mrpContextDTO.getMrpDemandList())) {
            for (MrpDemandDTO mrpDemandDTO : mrpContextDTO.getMrpDemandList()) {
                String newProductCode = GlassMrpUtil.getNewProductCode(mrpDemandDTO.getProductCode(), mrpContextDTO.getMixSubstitution());
                mrpDemandDTO.setProductCode(newProductCode);
            }
        }
        // 重置本厂库存供应物料编码
        if (CollectionUtils.isNotEmpty(mrpContextDTO.getFactorySupplyList())) {
            for (MrpSupplyDTO mrpSupplyDTO : mrpContextDTO.getFactorySupplyList()) {
                String newProductCode = GlassMrpUtil.getNewProductCode(mrpSupplyDTO.getProductCode(), mrpContextDTO.getMixSubstitution());
                mrpSupplyDTO.setProductCode(newProductCode);
            }
        }
        // 重置港口库存供应物料编码
        if (CollectionUtils.isNotEmpty(mrpContextDTO.getPortSupplyList())) {
            for (MrpSupplyDTO mrpSupplyDTO : mrpContextDTO.getPortSupplyList()) {
                String newProductCode = GlassMrpUtil.getNewProductCode(mrpSupplyDTO.getProductCode(), mrpContextDTO.getMixSubstitution());
                mrpSupplyDTO.setProductCode(newProductCode);
            }
        }
        // 重置浮法库存供应物料编码
        if (CollectionUtils.isNotEmpty(mrpContextDTO.getFloatSupplyList())) {
            for (MrpSupplyDTO mrpSupplyDTO : mrpContextDTO.getFloatSupplyList()) {
                String newProductCode = GlassMrpUtil.getNewProductCode(mrpSupplyDTO.getProductCode(), mrpContextDTO.getMixSubstitution());
                mrpSupplyDTO.setProductCode(newProductCode);
            }
        }
        // 重置原片替代映射物料编码
        if (CollectionUtils.isNotEmpty(mrpContextDTO.getGlassSubstitutionRelationshipList())) {
            for (GlassSubstitutionRelationshipVO glassSubstitutionRelationshipVO : mrpContextDTO.getGlassSubstitutionRelationshipList()) {
                // ERP-BOM(主料)
                if (org.apache.commons.lang3.StringUtils.isNotBlank(glassSubstitutionRelationshipVO.getRawProductCode())){
                    String newProductCode = GlassMrpUtil.getNewProductCode(glassSubstitutionRelationshipVO.getRawProductCode(), mrpContextDTO.getMixSubstitution());
                    glassSubstitutionRelationshipVO.setRawProductCode(newProductCode);
                }

                // 替代BOM
                if (org.apache.commons.lang3.StringUtils.isNotBlank(glassSubstitutionRelationshipVO.getSubstituteProductCode())){
                    String newSubstituteProductCode = GlassMrpUtil.getNewProductCode(glassSubstitutionRelationshipVO.getSubstituteProductCode(), mrpContextDTO.getMixSubstitution());
                    glassSubstitutionRelationshipVO.setSubstituteProductCode(newSubstituteProductCode);
                }


                //生产BOM(浮法库存)
                if (org.apache.commons.lang3.StringUtils.isNotBlank(glassSubstitutionRelationshipVO.getProductionSubstituteProductCode())){
                    String productionSubstituteProductCode = GlassMrpUtil.getNewProductCode(glassSubstitutionRelationshipVO.getProductionSubstituteProductCode(), mrpContextDTO.getMixSubstitution());
                    glassSubstitutionRelationshipVO.setProductionSubstituteProductCode(productionSubstituteProductCode);
                }
            }

            // 按照MRP-BOM进行分组
            Map<String, List<GlassSubstitutionRelationshipVO>> glassSubstitutionRelationshipMapOfRawProductCode = mrpContextDTO.getGlassSubstitutionRelationshipList()
                    .stream().collect(Collectors.groupingBy(GlassSubstitutionRelationshipVO::getRawProductCode));
            mrpContextDTO.setGlassSubstitutionRelationshipMapOfRawProductCode(glassSubstitutionRelationshipMapOfRawProductCode);

            // 按照生产BOM进行分组
            Map<String, List<GlassSubstitutionRelationshipVO>> glassSubstitutionRelationshipMapOfProductionCode = mrpContextDTO.getGlassSubstitutionRelationshipList()
                    .stream().filter(item -> org.apache.commons.lang3.StringUtils.isNotBlank(item.getProductionSubstituteProductCode()))
                    .collect(Collectors.groupingBy(GlassSubstitutionRelationshipVO::getProductionSubstituteProductCode));
            mrpContextDTO.setGlassSubstitutionRelationshipMapOfProductionCode(glassSubstitutionRelationshipMapOfProductionCode);
        }
    }

    protected void setMixSubstitution(MrpContextDTO mrpContextDTO) {
        List<String> ypProductCodes = mdsFeign.getYpProductCodes(mrpContextDTO.getScenario());
        Map<String, List<String>> substitutionProductGroup = GlassMrpUtil.getMixProductCodeGroup(ypProductCodes);
        mrpContextDTO.setMixSubstitution(substitutionProductGroup);
    }

    private void setMaterialDayTotalDemand(MrpContextDTO mrpContextDTO) {

        List<MaterialDayTotalDemandDTO> materialDaysDemandList = new ArrayList<>();
        List<Date> inventoryShiftDateList = getInventoryShiftDateList(mrpContextDTO.getMrpCalcDate(), 60);

        Set<String> productCodeSet = new HashSet<>();
        // mrpDemandList过滤出是物料类型是原片的需求
        List<MrpDemandDTO> glassDemandList = mrpContextDTO.getMrpDemandList().stream()
                .filter(t -> t.getProductClassify().equals("RA.A"))
                .peek(t -> {
                    t.setDemandQuantity(t.getDemandQuantity().setScale(0, RoundingMode.UP));
                    t.setUnFulfillmentQuantity(t.getDemandQuantity());
                    productCodeSet.add(t.getProductCode());
                })
                .collect(Collectors.toList());
        // 计划替代
        List<InventoryAlternativeRelationshipVO> inventoryReplaceList = mrpContextDTO.getInventoryAlternativeRelationshipList();
        if (CollectionUtils.isNotEmpty(inventoryReplaceList)) {
            inventoryReplaceList.forEach(t -> {
                productCodeSet.add(t.getDemandProductCode());
                productCodeSet.add(t.getReplacedProductCode());
            });
        }
        Map<String, List<MrpDemandDTO>> mrpDemandGroup = glassDemandList.stream()
                .collect(Collectors.groupingBy(t -> String.join("&&", t.getProductCode(), DateUtils.dateToString(t.getDemandTime()))));
        for (Date inventoryDate : inventoryShiftDateList) {
            for (String productCode : productCodeSet) {
                String key = String.join("&&", productCode, DateUtils.dateToString(inventoryDate));
                BigDecimal standardDemand = BigDecimal.ZERO;
                List<MrpDemandDTO> productDayDemandList = mrpDemandGroup.get(key);
                if (CollectionUtils.isNotEmpty(productDayDemandList)) {
                    standardDemand = productDayDemandList.stream().map(MrpDemandDTO::getDemandQuantity)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }

                // 被替代的量
                BigDecimal useReplaceQuantity = BigDecimal.ZERO;
                List<MaterialDayTotalDemandDTO.ReplaceDetailDTO> useReplaceDetailList = new ArrayList<>();
                if (standardDemand.compareTo(BigDecimal.ZERO) > 0 && CollectionUtils.isNotEmpty(inventoryReplaceList)) {
                    List<InventoryAlternativeRelationshipVO> replaceList = inventoryReplaceList.stream()
                            .filter(t -> t.getDemandProductCode().equals(productCode) && !t.getStartTime().after(inventoryDate))
                            .sorted(Comparator.comparing(InventoryAlternativeRelationshipVO::getPriority))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(replaceList)) {
                        BigDecimal demadQuantity = standardDemand;
                        for (InventoryAlternativeRelationshipVO inventoryReplace : replaceList) {
                            if (inventoryReplace.getUseReplaceRemainingQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                                continue;
                            }
                            String replaceProductCode = inventoryReplace.getReplacedProductCode();
                            BigDecimal replaceQuantity = getReplaceQuantity(inventoryReplace, demadQuantity, true);
                            useReplaceQuantity = useReplaceQuantity.add(replaceQuantity);
                            demadQuantity = demadQuantity.subtract(useReplaceQuantity);

                            MaterialDayTotalDemandDTO.ReplaceDetailDTO replaceDetailDTO = new MaterialDayTotalDemandDTO.ReplaceDetailDTO();
                            replaceDetailDTO.setProductCode(replaceProductCode);
                            replaceDetailDTO.setReplaceQuantity(replaceQuantity);
                            replaceDetailDTO.setRemark(inventoryReplace.getRemark());
                            // 添加这一行，记录替代关系的ID
                            replaceDetailDTO.setInventoryAlternativeRelationshipId(inventoryReplace.getId());
                            useReplaceDetailList.add(replaceDetailDTO);
                            if (demadQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                                break;
                            }
                        }
                    }
                }
                // 替代规格需求量
                BigDecimal usedAsReplaceQuantityTotal = BigDecimal.ZERO;
                Map<String, BigDecimal> useAsReplaceDetail = new HashMap<>();
                if (CollectionUtils.isNotEmpty(inventoryReplaceList)) {
                    List<InventoryAlternativeRelationshipVO> usedAsReplaceConfigList = inventoryReplaceList.stream()
                            .filter(t -> t.getReplacedProductCode().equals(productCode) && !t.getStartTime().after(inventoryDate))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(usedAsReplaceConfigList)) {
                        for (InventoryAlternativeRelationshipVO inventoryReplace : usedAsReplaceConfigList) {
                            if (inventoryReplace.getUsedAsReplaceRemainingQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                                continue;
                            }
                            String demandProductCode = inventoryReplace.getDemandProductCode();
                            // 被替代的物料key
                            String demandKey = String.join("&&", demandProductCode, DateUtils.dateToString(inventoryDate));
                            BigDecimal demandQuantity = mrpDemandGroup.get(demandKey) == null ? BigDecimal.ZERO
                                    : mrpDemandGroup.get(demandKey).stream().map(MrpDemandDTO::getDemandQuantity)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            if (demandQuantity.compareTo(BigDecimal.ZERO) > 0) {
                                BigDecimal usedAsReplaceQuantity = getReplaceQuantity(inventoryReplace, demandQuantity, false);
                                usedAsReplaceQuantityTotal = usedAsReplaceQuantityTotal.add(usedAsReplaceQuantity);
                                BigDecimal useAsReplaceQuantityByDemandProduct = useAsReplaceDetail.get(demandProductCode)
                                        == null ? usedAsReplaceQuantity : useAsReplaceDetail.get(demandProductCode).add(usedAsReplaceQuantity);
                                useAsReplaceDetail.put(demandProductCode, useAsReplaceQuantityByDemandProduct);
                            }
                        }
                    }

                }
                if (standardDemand.compareTo(BigDecimal.ZERO) == 0
                        && useReplaceQuantity.compareTo(BigDecimal.ZERO) == 0
                        && usedAsReplaceQuantityTotal.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }

                MaterialDayTotalDemandDTO materialDayTotalDemandDTO = new MaterialDayTotalDemandDTO();
                materialDayTotalDemandDTO.setDemandDate(inventoryDate);
                materialDayTotalDemandDTO.setProductCode(productCode);
                materialDayTotalDemandDTO.setStandardDemand(standardDemand);
                materialDayTotalDemandDTO.setUseReplaceQuantity(useReplaceQuantity);
                materialDayTotalDemandDTO.setUseReplaceDetailList(useReplaceDetailList);
                materialDayTotalDemandDTO.setUsedAsReplaceQuantity(usedAsReplaceQuantityTotal);
                materialDayTotalDemandDTO.setUseAsReplaceDetail(useAsReplaceDetail);
                materialDaysDemandList.add(materialDayTotalDemandDTO);
            }
        }

        mrpContextDTO.setMaterialDayTotalDemandList(materialDaysDemandList);
    }

    private void setFreeStorage(MrpContextDTO mrpContextDTO) {
        List<ProFreeStoragePO> proFreeStoragePOS = proFreeStorageDao.selectByParams(new HashMap<>());
        Map<String, Integer> freeStorageMap = proFreeStoragePOS.stream()
                .collect(Collectors.toMap(t -> String.join("&&", t.getCarrierCode(), t.getPortCode()),
                        t -> Integer.valueOf(t.getFreeStorage()), (v1, v2) -> v1));
        mrpContextDTO.setPortFreeStorageMap(freeStorageMap);
    }


    private void setInventoryPlanReplaceData(MrpContextDTO mrpContextDTO) {
        // 库存批次可用查找替代计划数据
        List<InventoryAlternativeRelationshipVO> inventoryAlternativeRelationshipList = inventoryAlternativeRelationshipService.selectAll();
        if (CollectionUtils.isNotEmpty(inventoryAlternativeRelationshipList)) {
            inventoryAlternativeRelationshipList.forEach(t -> {
                t.setUseReplaceRemainingQuantity(t.getAlternativeQuantity());
                t.setUsedAsReplaceRemainingQuantity(t.getAlternativeQuantity());
            });
        }
        mrpContextDTO.setInventoryAlternativeRelationshipList(inventoryAlternativeRelationshipList);
    }

    private void setFloatSupplyList(MrpContextDTO mrpContextDTO) {
        // 按产品编码分组统计
        List<InventoryFloatGlassDetailVO> inventoryFloatGlassDetailVOS = inventoryFloatGlassDetailService.selectProductInventory();
        // 浮法调拨计划根据物料、浮法库存点分组
        Map<String, BigDecimal> materialPlanTransferGroup = mrpContextDTO.getMaterialPlanTransferList()
                .stream()
                .filter(t -> mrpContextDTO.getFloatStockPointCodeList().contains(t.getStockPointCodeFrom()))
                .collect(Collectors.groupingBy(MaterialPlanTransferVO::getGroupKey,
                        Collectors.reducing(BigDecimal.ZERO, MaterialPlanTransferVO::getTransferQuantity, BigDecimal::add)));
        // 原片浮法厂库存
        List<MrpSupplyDTO> floatSupplyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inventoryFloatGlassDetailVOS)) {
            for (InventoryFloatGlassDetailVO t : inventoryFloatGlassDetailVOS) {
                if (null == t.getStorageTime()) {
                    continue;
                }
                String key = String.join("&&", t.getProductCode(), t.getStockPointCode());
                BigDecimal transferQuantity = materialPlanTransferGroup.getOrDefault(key, BigDecimal.ZERO);

                MrpSupplyDTO mrpSupplyDTO = createMrpSupply(String.join("&&", t.getProductCode(), t.getStockPointCode()),
                        t.getProductCode(),
                        t.getStockPointCode(),
                        MrpSupplySourceEnum.FLOAT_INVENTORY.getCode(),
                        t.getStorageTime(),
                        t.getQty().subtract(transferQuantity));
                mrpSupplyDTO.setUnitUsageQuantity(t.getPerBox());
                floatSupplyList.add(mrpSupplyDTO);
            }
        }

        if (CollectionUtils.isNotEmpty(floatSupplyList)) {
            floatSupplyList.sort(Comparator.comparing(MrpSupplyDTO::getSupplyTime));
        }
        mrpContextDTO.setFloatSupplyList(floatSupplyList);
    }

    private void setPortSupplyList(MrpContextDTO mrpContextDTO) {
        // 码头调拨计划
        List<String> portTransferIds = mrpContextDTO.getMaterialPlanTransferList().stream()
                .filter(t -> mrpContextDTO.getPortStockPointCodeList().contains(t.getStockPointCodeFrom()))
                .map(MaterialPlanTransferVO::getId).collect(Collectors.toList());
        // 码头调拨计划库存批次占用信息
        Map<String, BigDecimal> portInventoryOccupyGroup = new HashMap<>();
        if (CollectionUtils.isNotEmpty(portTransferIds)) {
            // 根据库存批次id信息分组统计
            portInventoryOccupyGroup = mrpContextDTO.getMaterialPlanTransferInventoryDetailList().stream()
                    .collect(Collectors.groupingBy(MaterialPlanTransferInventoryDetailVO::getInventoryId,
                            Collectors.reducing(BigDecimal.ZERO, MaterialPlanTransferInventoryDetailVO::getTransferQuantity, BigDecimal::add)));
        }

        // 查询原片港口库存
        List<InventoryQuayDetailVO> inventoryQuayDetailVOS = inventoryQuayDetailService.selectByParams(ImmutableMap.of(
                "delivered", YesOrNoEnum.NO.getCode(), "enabled", YesOrNoEnum.YES.getCode()));
        Map<String, NewStockPointVO> stockPointVOMap = mrpContextDTO.getStockPointVOList().stream()
                .collect(Collectors.toMap(NewStockPointVO::getStockPointName, Function.identity()));
        // 原片港口库存(港口现有库存+港口在途库存)
        List<MrpSupplyDTO> portSupplyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inventoryQuayDetailVOS)){
            for (InventoryQuayDetailVO inventoryQuayDetailVO : inventoryQuayDetailVOS) {
                NewStockPointVO newStockPointVO = stockPointVOMap.get(inventoryQuayDetailVO.getPortName());
                if (Objects.isNull(newStockPointVO) || null != inventoryQuayDetailVO.getContainerDeliveryTime() || null == inventoryQuayDetailVO.getActualArrivalTime()) {
                    continue;
                }
                MrpSupplyDTO mrpSupplyDTO = createMrpSupply(inventoryQuayDetailVO.getId(),
                        inventoryQuayDetailVO.getProductCode(),
                        newStockPointVO.getStockPointCode(),
                        MrpSupplySourceEnum.PORT_INVENTORY.getCode(),
                        inventoryQuayDetailVO.getActualArrivalTime(),
                        inventoryQuayDetailVO.getActualSentQuantity().subtract(portInventoryOccupyGroup.getOrDefault(inventoryQuayDetailVO.getId(), BigDecimal.ZERO)));
                mrpSupplyDTO.setCarrierCode(inventoryQuayDetailVO.getCarrier());
                mrpSupplyDTO.setUnitUsageQuantity(BigDecimal.ONE);
                mrpSupplyDTO.setContainerNumber(inventoryQuayDetailVO.getContainerNumber());
                portSupplyList.add(mrpSupplyDTO);
            }
        }


        // 查询港口原片在途库存
        List<InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailVOS =
                inventoryFloatGlassShippedDetailService.selectByParams(ImmutableMap.of("shipmentMethod", "国内船运",
                        "enabled", YesOrNoEnum.YES.getCode()));
        inventoryFloatGlassShippedDetailVOS = inventoryFloatGlassShippedDetailVOS.stream()
                .filter(item -> null != item.getActualSentQuantity())
                .collect(Collectors.toList());
        // 将码头在途转换成码头库存供应
        for (InventoryFloatGlassShippedDetailVO inventoryFloatGlassShippedDetailVO : inventoryFloatGlassShippedDetailVOS) {
            NewStockPointVO newStockPointVO = stockPointVOMap.get(inventoryFloatGlassShippedDetailVO.getPortName());
            if (Objects.isNull(inventoryFloatGlassShippedDetailVO.getEstimatedArrivalTime()) || null == newStockPointVO) {
                continue;
            }
            Date arriveTime = DateUtils.getDayFirstTime(inventoryFloatGlassShippedDetailVO.getEstimatedArrivalTime());
            Date supplyTime = DateUtils.moveDay(arriveTime, 1);
            MrpSupplyDTO mrpSupplyDTO = createMrpSupply(inventoryFloatGlassShippedDetailVO.getId(),
                    inventoryFloatGlassShippedDetailVO.getProductCode(), newStockPointVO.getStockPointCode(),
                    MrpSupplySourceEnum.PORT_ON_WAY.getCode(), supplyTime,
                    inventoryFloatGlassShippedDetailVO.getActualSentQuantity().subtract(portInventoryOccupyGroup.getOrDefault(inventoryFloatGlassShippedDetailVO.getId(), BigDecimal.ZERO)));
            mrpSupplyDTO.setUnitUsageQuantity(mrpSupplyDTO.getUnAllocatedQuantity());
            mrpSupplyDTO.setContainerNumber(inventoryFloatGlassShippedDetailVO.getContainerNumber());
            portSupplyList.add(mrpSupplyDTO);
        }
        portSupplyList.sort(Comparator.comparing(MrpSupplyDTO::getSupplyTime));
        mrpContextDTO.setPortSupplyList(portSupplyList);
    }

    private void setFactorySupplyList(MrpContextDTO mrpContextDTO) {
        // 原片本厂库存
        List<MrpSupplyDTO> factorySupplyList = new ArrayList<>();

        String bcStockPointCode = String.join("&&", mrpContextDTO.getBcStockPointCodeList());

        // 本厂现有库存
        List<InventoryBatchDetailVO> glassInventoryBatchDetailList = dfpFeign.selectAllGlassInventoryBatch(mrpContextDTO.getScenario());
        if (CollectionUtils.isNotEmpty(glassInventoryBatchDetailList)) {
            for (InventoryBatchDetailVO inventoryBatchDetailVO : glassInventoryBatchDetailList) {
                MrpSupplyDTO mrpSupplyDTO = createMrpSupply(inventoryBatchDetailVO.getId(),
                        inventoryBatchDetailVO.getProductCode(), bcStockPointCode,
                        MrpSupplySourceEnum.FACTORY_INVENTORY.getCode(),
                        DateUtils.stringToDate(inventoryBatchDetailVO.getAssignedTime(), DateUtils.COMMON_DATE_STR1),
                        BigDecimal.valueOf(Long.parseLong(inventoryBatchDetailVO.getCurrentQuantity())));
                factorySupplyList.add(mrpSupplyDTO);
            }
        }
        // 本厂在途库存
        List<InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailVOS =
                inventoryFloatGlassShippedDetailService.selectByParams(ImmutableMap.of("shipmentMethod", "汽车运输",
                        "enable", YesOrNoEnum.YES.getCode()));
        if (CollectionUtils.isNotEmpty(inventoryFloatGlassShippedDetailVOS)) {
            for (InventoryFloatGlassShippedDetailVO inventoryFloatGlassShippedDetailVO : inventoryFloatGlassShippedDetailVOS) {
                if (inventoryFloatGlassShippedDetailVO.getEstimatedArrivalTime() == null) {
                    continue;
                }
                Date arriveTime = DateUtils.getDayFirstTime(inventoryFloatGlassShippedDetailVO.getEstimatedArrivalTime());
                Date supplyTime = DateUtils.moveDay(arriveTime, 1);
                MrpSupplyDTO mrpSupplyDTO = createMrpSupply(inventoryFloatGlassShippedDetailVO.getId(),
                        inventoryFloatGlassShippedDetailVO.getProductCode(), bcStockPointCode,
                        MrpSupplySourceEnum.FACTORY_ON_WAY_FROM_FLOAT.getCode(), supplyTime, inventoryFloatGlassShippedDetailVO.getActualSentQuantity());
                factorySupplyList.add(mrpSupplyDTO);
            }
        }
        // 调拨到达时间不晚于mrp计算开始时间，且发货地是码头，目的地是本厂的调拨作为第一天的在途数据
        if (CollectionUtils.isNotEmpty(mrpContextDTO.getMaterialPlanTransferList())) {
            List<MaterialPlanTransferVO> materialPlanTransferVOList = mrpContextDTO.getMaterialPlanTransferList()
                    .stream()
                    .filter(t -> t.getTransferDateArrive().before(DateUtils.moveDay(mrpContextDTO.getMrpCalcDate(), 1)))
                    .filter(t -> mrpContextDTO.getPortStockPointCodeList().contains(t.getStockPointCodeFrom()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(materialPlanTransferVOList)) {
                for (MaterialPlanTransferVO materialPlanTransferVO : materialPlanTransferVOList) {
                    MrpSupplyDTO mrpSupplyDTO = createMrpSupply(materialPlanTransferVO.getId(),
                            materialPlanTransferVO.getProductCode(), materialPlanTransferVO.getStockPointCodeTo(),
                            MrpSupplySourceEnum.FACTORY_ON_WAY_FROM_PORT.getCode(), mrpContextDTO.getMrpCalcDate(), materialPlanTransferVO.getTransferQuantity());
                    factorySupplyList.add(mrpSupplyDTO);
                }
            }
        }
        factorySupplyList.sort(Comparator.comparing(MrpSupplyDTO::getSupplyTime));
        mrpContextDTO.setFactorySupplyList(factorySupplyList);
    }

    private void setMaterialPlanTransferList(MrpContextDTO mrpContextDTO) {
        // 已发布未入库的调拨计划
        Map<String, Object> params = new HashMap<>();
        params.put("storageFlag", YesOrNoEnum.NO.getCode());
        params.put("transferStatus", TransferStatusEnum.PUBLISHED.getCode());
        List<MaterialPlanTransferVO> materialPlanTransferList = materialPlanTransferService.selectByParams(params);
        mrpContextDTO.setMaterialPlanTransferList(materialPlanTransferList);
        // 调拨计划对应的库存批次信息
        if (CollectionUtils.isNotEmpty(materialPlanTransferList)) {
            List<String> materialPlanTransferIds = materialPlanTransferList.stream().map(MaterialPlanTransferVO::getId).collect(Collectors.toList());
            List<MaterialPlanTransferInventoryDetailVO> transferInventoryDetailList = materialPlanTransferInventoryDetailService
                    .selectByParams(ImmutableMap.of("materialPlanTransferIds", materialPlanTransferIds));
            mrpContextDTO.setMaterialPlanTransferInventoryDetailList(transferInventoryDetailList);
        }
    }

    private MrpSupplyDTO createMrpSupply(String supplyId,
                                         String productCode,
                                         String stockPointCode,
                                         String supplySource,
                                         Date supplyTime,
                                         BigDecimal supplyQuantity) {
        MrpSupplyDTO mrpSupplyDTO = new MrpSupplyDTO();
        mrpSupplyDTO.setSupplyId(supplyId);
        mrpSupplyDTO.setProductCode(productCode);
        mrpSupplyDTO.setOriginProductCode(productCode);
        mrpSupplyDTO.setStockPointCode(stockPointCode);
        mrpSupplyDTO.setSupplyQuantity(supplyQuantity);
        mrpSupplyDTO.setUnAllocatedQuantity(supplyQuantity);
        mrpSupplyDTO.setSupplyTime(supplyTime);
        mrpSupplyDTO.setSupplySource(supplySource);
        return mrpSupplyDTO;
    }

    protected static Map<String, MaterialDayTotalDemandDTO> getMaterialDayTotalDemand(MrpContextDTO mrpContextDTO) {
        Map<String, MaterialDayTotalDemandDTO> demandByProductAndDate = new HashMap<>();
        if (CollectionUtils.isNotEmpty(mrpContextDTO.getMaterialDayTotalDemandList())) {
            demandByProductAndDate = mrpContextDTO.getMaterialDayTotalDemandList().stream()
                    .collect(Collectors.toMap(t ->
                                    String.join("&&", t.getProductCode(), DateUtils.dateToString(t.getDemandDate())),
                            Function.identity()));
        }
        return demandByProductAndDate;
    }

    protected static Map<String, List<MaterialDayTotalDemandDTO>> getDemandGroupByProduct(MrpContextDTO mrpContextDTO) {
        Map<String, List<MaterialDayTotalDemandDTO>> demandGroupByProduct = new HashMap<>();
        if (CollectionUtils.isNotEmpty(mrpContextDTO.getMaterialDayTotalDemandList())) {
            demandGroupByProduct = mrpContextDTO.getMaterialDayTotalDemandList().stream()
                    .collect(Collectors.groupingBy(MaterialDayTotalDemandDTO::getProductCode));
        }
        return demandGroupByProduct;
    }


    protected static GlassSafetyInventoryVO getGlassSafetyInventory(MrpContextDTO mrpContextDTO,
                                                                    String productCode,
                                                                    MrpResultPO mrpResultPO) {
        String productThickness = productCode.substring(11, 15);
        String productColor = productCode.substring(15);
        String thickness = BigDecimal.valueOf(Integer.parseInt(productThickness)).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString();
        thickness = thickness.substring(0, thickness.indexOf(".") + 2).replace(".0", "");
        String safeInventoryKey = productColor + "&" + thickness;
        GlassSafetyInventoryVO glassSafetyInventoryVO = mrpContextDTO.getGlassSafetyInventoryMap().get(safeInventoryKey);
        if (Objects.isNull(glassSafetyInventoryVO)) {
            mrpResultPO.getErrorMsgList().add("物料编码" + productCode + "颜色" + productColor + "厚度" + productThickness + "在原片安全库存中不存在，取*通用库存");
            glassSafetyInventoryVO = mrpContextDTO.getGlassSafetyInventoryMap().get("*&*");
        }
        return glassSafetyInventoryVO;
    }

    protected void transferToBc(MrpContextDTO mrpContextDTO,
                                List<GlassInventoryShiftDataPO> glassInventoryShiftDataCreateList,
                                Map<String, BigDecimal> factoryOpeningInventory,
                                Map<String, MaterialDayTotalDemandDTO> demandByProductAndDate,
                                List<MaterialPlanInventoryOccupyPO> portInventoryOccupyList,
                                List<MaterialPlanInventoryOccupyPO> floatInventoryOccupyList,
                                List<GlassInventoryShiftDetailPO> bcInventoryShiftCreateList,
                                Map<String, BigDecimal> usedAsReplaceQuantityMap,
                                boolean whetherBreak) {
        List<Date> inventoryShiftDateList = getInventoryShiftDateList(mrpContextDTO.getMrpCalcDate(), 12);

        Map<String, GlassInventoryShiftDetailPO> inventoryShiftDetailPOMap = bcInventoryShiftCreateList.stream()
                .collect(Collectors.toMap(item -> String.join("#", item.getInventoryShiftDataId(), DateUtils.dateToString(item.getInventoryDate())),
                        Function.identity()));

        Map<String, BigDecimal> factoryOpeningInventoryHasAdjust = new HashMap<>(factoryOpeningInventory);
        Map<String, GlassInventoryShiftDetailVO> fixedInventoryShiftMap = new HashMap<>();
        if (mrpContextDTO.isBcAdjust()) {
            fixedInventoryShiftMap = mrpContextDTO.getBcGlassInventoryShiftDetailList().stream()
                    .filter(t -> !t.getInventoryDate().after(mrpContextDTO.getAdjustInventoryShiftDetail().getInventoryDate()))
                    .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getInventoryDate()), Function.identity()));
        }
        Map<String, List<GlassSubstitutionRelationshipVO>> glassSubstitutionRelationshipMapOfProductionCode = mrpContextDTO.getGlassSubstitutionRelationshipMapOfProductionCode();
        for (GlassInventoryShiftDataPO inventoryShiftData : glassInventoryShiftDataCreateList) {
            String productCode = inventoryShiftData.getProductCode();
            List<String> mixSubstitutionList;
            if (mrpContextDTO.getMixSubstitution().containsKey(productCode)){
                mixSubstitutionList = mrpContextDTO.getMixSubstitution().get(productCode);
            }else {
                mixSubstitutionList = new ArrayList<>();
                mixSubstitutionList.add(productCode);
            }
            // 获取原片的替代数据(生产BOM获取)
            List<GlassSubstitutionRelationshipVO> glassProductionSubstitutionList = glassSubstitutionRelationshipMapOfProductionCode.get(productCode);
            // 获取ERP-BOM(主料)
            String mainProductCode = getMainProductCode(productCode, glassProductionSubstitutionList);
            // 获取主料(ERP-BOM)是否禁用
            boolean rawProductInventoryDisabledFlag = getRawProductInventoryDisabledFlag(glassProductionSubstitutionList);
            if (!productCode.equals(mainProductCode) && !rawProductInventoryDisabledFlag){
                // 创建主料替代
                GlassSubstitutionRelationshipVO glassSubstitutionRelationshipVO = createMainGlassSubstitutionRelationship(mainProductCode);
                glassProductionSubstitutionList.add(glassSubstitutionRelationshipVO);
            }

            for (int i = 0; i < inventoryShiftDateList.size(); i++) {
                Date inventoryShiftDate = inventoryShiftDateList.get(i);
                // 预计算目标时间：将 inventoryShiftDate 加 1 天，避免循环内重复计算
                Date targetSupplyTime = DateUtils.moveDay(inventoryShiftDate, 1);
                String inventoryShiftDateStr = DateUtils.dateToString(inventoryShiftDate);
                GlassInventoryShiftDetailPO bcInventoryShiftDetail = inventoryShiftDetailPOMap.get(String.join("#", inventoryShiftData.getId(), inventoryShiftDateStr));
//                GlassInventoryShiftDetailPO bcInventoryShiftDetail = new GlassInventoryShiftDetailPO();
//                bcInventoryShiftDetail.setId(UUIDUtil.getUUID());
//                bcInventoryShiftDetail.setInventoryShiftDataId(inventoryShiftData.getId());
//                bcInventoryShiftDetail.setInventoryDate(inventoryShiftDate);
                String stockPointCode = String.join("&&", mrpContextDTO.getBcStockPointCodeList());
                bcInventoryShiftDetail.setStockPointType(MrpStockPointTypeEnum.BC.getCode());
                bcInventoryShiftDetail.setStockPointCode(stockPointCode);
                // 期初库存
//                BigDecimal openingInventory = factoryOpeningInventory.get(mainProductCode) == null ? BigDecimal.ZERO : factoryOpeningInventory.get(mainProductCode);
//                bcInventoryShiftDetail.setOpeningInventory(openingInventory);
                BigDecimal openingInventory;
                if (i == 0){
                    openingInventory = bcInventoryShiftDetail.getOpeningInventory();
                }else {
                    openingInventory = factoryOpeningInventoryHasAdjust.get(productCode);
                }
                bcInventoryShiftDetail.setOpeningInventory(openingInventory);

                // 免堆期运入
                freeStorageTransport(productCode, mrpContextDTO, inventoryShiftDate, inventoryShiftDateList, bcInventoryShiftDetail, portInventoryOccupyList);

                // 总需求
                String demandKey = String.join("&&", productCode, DateUtils.dateToString(inventoryShiftDate));
                MaterialDayTotalDemandDTO materialDaysDemandDO = demandByProductAndDate.get(demandKey);

                // 标准规格需求
                BigDecimal standardDemand = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getStandardDemand();
                bcInventoryShiftDetail.setDemandQuantity(standardDemand);
                // 已满足标准规格需求
                BigDecimal satisfyStandardDemand = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getSatisfyStandardDemand();
                // 标准规格需求 - 已满足标准规格需求
                standardDemand = standardDemand.subtract(satisfyStandardDemand);
                // 替代规格需求
                BigDecimal usedAsReplaceQuantity = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getUsedAsReplaceQuantity();
                String joinKey = String.join("#", productCode, inventoryShiftDateStr);
                // 考虑二次计算再加上替代数量
                if (usedAsReplaceQuantityMap.containsKey(joinKey) && whetherBreak){
                    usedAsReplaceQuantity = usedAsReplaceQuantity.add(usedAsReplaceQuantityMap.get(joinKey));
                }
                bcInventoryShiftDetail.setUsedAsReplaceQuantity(usedAsReplaceQuantity);
                // 被替代的量
                BigDecimal useReplaceQuantity = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getUseReplaceQuantity();
                bcInventoryShiftDetail.setUseReplaceQuantity(useReplaceQuantity);

                // 已发布调拨-码头
                BigDecimal transitQuantityFromPort = mrpContextDTO.getMaterialPlanTransferList().parallelStream()
                        .filter(t -> t.getTransferDateArrive().compareTo(inventoryShiftDate) == 0
                                && mrpContextDTO.getBcStockPointCodeList().contains(t.getStockPointCodeTo())
                                && mrpContextDTO.getPortStockPointCodeList().contains(t.getStockPointCodeFrom())
                                && mrpContextDTO.getMixSubstitution().get(productCode).contains(t.getProductCode()))
                        .collect(Collectors.toList()).stream().map(MaterialPlanTransferVO::getTransferQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                bcInventoryShiftDetail.setTransitQuantityFromPort(transitQuantityFromPort);

                // 已发布调拨（浮法汽运）
                BigDecimal planInputQuantity = mrpContextDTO.getMaterialPlanTransferList().parallelStream()
                        .filter(t -> stockPointCode.contains(t.getStockPointCodeTo())
                                && mrpContextDTO.getFloatStockPointCodeList().contains(t.getStockPointCodeFrom())
                                && t.getTransferDateArrive().compareTo(inventoryShiftDate) == 0
                                && mrpContextDTO.getMixSubstitution().get(productCode).contains(t.getProductCode()))
                        .map(MaterialPlanTransferVO::getTransferQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                bcInventoryShiftDetail.setInputQuantity(planInputQuantity);

                // 在途（浮法汽运）
                BigDecimal transitQuantityFromFloat = mrpContextDTO.getFactorySupplyList().parallelStream()
                        .filter(t -> targetSupplyTime.compareTo(t.getSupplyTime()) == 0 &&
                                t.getProductCode().equals(productCode) &&
                                t.getSupplySource().equals(MrpSupplySourceEnum.FACTORY_ON_WAY_FROM_FLOAT.getCode()))
                        .collect(Collectors.toList()).stream().map(MrpSupplyDTO::getSupplyQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                bcInventoryShiftDetail.setTransitQuantityFromFloat(transitQuantityFromFloat);


                // 总需求 = 标准规格需求 + 替代规格需求 - 被替代的量 + 已满足标准规格需求
                BigDecimal totalDemand = standardDemand.add(usedAsReplaceQuantity).subtract(useReplaceQuantity);
                // 调拨前期末库存 = 期初库存 + 在途 + 已发布计划入柜量 - 总需求
//                BigDecimal beforeEndingInventory = openingInventory.add(transitQuantityFromPort).add(transitQuantityFromFloat)
//                        .add(planInputQuantity).subtract(totalDemand);
                //更新本厂库存
                if (factoryOpeningInventory.containsKey(productCode)){
                    if (factoryOpeningInventory.get(productCode).compareTo(BigDecimal.ZERO) > 0){
                        factoryOpeningInventory.put(productCode, factoryOpeningInventory.get(productCode).subtract(totalDemand));
                    }
                    if (factoryOpeningInventory.get(productCode).compareTo(BigDecimal.ZERO) < 0){
                        factoryOpeningInventory.put(productCode, BigDecimal.ZERO);
                    }
                }

                // 期初库存包含前一天调整量
                BigDecimal openingInventoryHasAdjust;
                if (i == 0){
                    openingInventoryHasAdjust = openingInventory;
                }else {
                    openingInventoryHasAdjust = factoryOpeningInventoryHasAdjust.get(productCode) == null ?
                            BigDecimal.ZERO : factoryOpeningInventoryHasAdjust.get(productCode);
                }

                // 用于计算调拨的期末库存
                BigDecimal endingInventoryForAdjust = openingInventoryHasAdjust.add(transitQuantityFromPort).add(transitQuantityFromFloat)
                        .add(planInputQuantity).subtract(totalDemand).subtract(satisfyStandardDemand);
                // 免堆期运入量
                BigDecimal adjustQuantityFromPortOfFreeStorage = portInventoryOccupyList.stream()
                        .filter(t -> mixSubstitutionList.contains(t.getSupplyProductCode())
                                && t.getTransportDateEnd().compareTo(inventoryShiftDate) == 0)
                        .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 最小安全库存水位
                bcInventoryShiftDetail.setSafetyStockLevelMin(getInventoryLevel(inventoryShiftData.getSafetyStockDaysMin(),
                        productCode, inventoryShiftDate, demandByProductAndDate));

                // 目标安全库存水位
                bcInventoryShiftDetail.setSafetyStockLevelStandard(getInventoryLevel(inventoryShiftData.getSafetyStockDaysStandard(),
                        productCode, inventoryShiftDate, demandByProductAndDate));

                // 最大安全库存水位
                bcInventoryShiftDetail.setSafetyStockLevelMax(getInventoryLevel(inventoryShiftData.getSafetyStockDaysMax(),
                        productCode, inventoryShiftDate, demandByProductAndDate));

                GlassInventoryShiftDetailVO materialPlanInventoryShiftVO = fixedInventoryShiftMap.get(DateUtils.dateToString(inventoryShiftDate));
                if (materialPlanInventoryShiftVO != null) {
                    if (materialPlanInventoryShiftVO.getAdjustQuantityFromPort() != null
                            && materialPlanInventoryShiftVO.getAdjustQuantityFromPort().compareTo(adjustQuantityFromPortOfFreeStorage) < 0) {
                        throw new BusinessException("计划调拨-码头送柜数量低于免堆期运入量");
                    }
                    // 码头库存调拨
                    BigDecimal adjustFromPort = materialPlanInventoryShiftVO.getAdjustQuantityFromPort() == null
                            ? BigDecimal.ZERO : materialPlanInventoryShiftVO.getAdjustQuantityFromPort();

                    BigDecimal unAdjustQuantity = allocateInventory(mrpContextDTO, productCode, bcInventoryShiftDetail,
                            portInventoryOccupyList, adjustFromPort, inventoryShiftDate,
                            MrpSupplySourceEnum.PORT_INVENTORY.getCode(), true,
                            false,null, usedAsReplaceQuantityMap);

                    if (unAdjustQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        throw new BusinessException("码头数量不足，无法满足计划调拨-码头送柜数量");
                    }

                    // 浮法库存调拨
                    BigDecimal adjustFromFloat = materialPlanInventoryShiftVO.getAdjustQuantityFromFloat() == null
                            ? BigDecimal.ZERO : materialPlanInventoryShiftVO.getAdjustQuantityFromFloat();

                    allocateInventory(mrpContextDTO, productCode, bcInventoryShiftDetail, floatInventoryOccupyList,
                            adjustFromFloat, inventoryShiftDate, MrpSupplySourceEnum.FLOAT_INVENTORY.getCode(), true,
                            false,null, usedAsReplaceQuantityMap);
                } else {
                    // 期望的期末库存
                    BigDecimal endingInventory = endingInventoryForAdjust.add(adjustQuantityFromPortOfFreeStorage);
                    BigDecimal adjustQuantity = getAdjustQuantity(endingInventory,
                            bcInventoryShiftDetail.getSafetyStockLevelMin(),
                            bcInventoryShiftDetail.getSafetyStockLevelStandard(),
                            bcInventoryShiftDetail.getSafetyStockLevelMax());

                    // 用替代料的<本厂库存去满足>
                    if (adjustQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        adjustQuantity = allocateGlassSubstitutionRelationship(adjustQuantity, inventoryShiftDateStr,bcInventoryShiftDetail,
                                factoryOpeningInventory, glassProductionSubstitutionList, usedAsReplaceQuantityMap);
                    }
                    if (adjustQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        // 码头库存调拨 -用主料的码头库存
                        adjustQuantity = allocateInventory(mrpContextDTO, productCode, bcInventoryShiftDetail,
                                portInventoryOccupyList, adjustQuantity, inventoryShiftDate,
                                MrpSupplySourceEnum.PORT_INVENTORY.getCode(), true,
                                true, glassProductionSubstitutionList, usedAsReplaceQuantityMap);
                    }

                    if (adjustQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        // 浮法库存调拨,只调拨生产BOM的
                        allocateInventory(mrpContextDTO, productCode, bcInventoryShiftDetail, floatInventoryOccupyList,
                                adjustQuantity, inventoryShiftDate, MrpSupplySourceEnum.FLOAT_INVENTORY.getCode(), true,
                                false, glassProductionSubstitutionList, usedAsReplaceQuantityMap);
                    }
                    // 替代后的调整数量
//                    if (adjustQuantity.compareTo(BigDecimal.ZERO) > 0) {
//                        // 码头库存调拨
//                        BigDecimal unFulfillmentQuantity = allocateInventory(mrpContextDTO, productCode, bcInventoryShiftDetail,
//                                portInventoryOccupyList, adjustQuantity, inventoryShiftDate,
//                                MrpSupplySourceEnum.PORT_INVENTORY.getCode(), true);
//                        // 浮法库存调拨
//                        allocateInventory(mrpContextDTO, productCode, bcInventoryShiftDetail, floatInventoryOccupyList,
//                                unFulfillmentQuantity, inventoryShiftDate, MrpSupplySourceEnum.FLOAT_INVENTORY.getCode(), true);
//                    }
                }


                // 码头整箱调拨
                if (CollectionUtils.isNotEmpty(portInventoryOccupyList)) {
                    Set<String> containerNumber = portInventoryOccupyList.stream()
                            .filter(t -> mixSubstitutionList.contains(t.getSupplyProductCode())
                                    && t.getTransportDateEnd().compareTo(inventoryShiftDate) == 0)
                            .map(MaterialPlanInventoryOccupyPO::getContainerNumber).collect(Collectors.toSet());
                    mtAllContainerTransfer(productCode, mrpContextDTO, inventoryShiftDate, bcInventoryShiftDetail,
                            portInventoryOccupyList, mrpContextDTO.getPortSupplyList(), containerNumber);
                }
                BigDecimal adjustQuantityFromPort = portInventoryOccupyList.stream()
                        .filter(t -> mixSubstitutionList.contains(t.getSupplyProductCode())
                                && t.getTransportDateEnd().compareTo(inventoryShiftDate) == 0)
                        .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                bcInventoryShiftDetail.setAdjustQuantityFromPort(adjustQuantityFromPort);

                // 浮法汽运调拨
                BigDecimal adjustQuantityFromFloat = floatInventoryOccupyList.stream()
                        .filter(t -> mixSubstitutionList.contains(t.getSupplyProductCode())
                                && t.getTransportDateEnd().compareTo(inventoryShiftDate) == 0
                                && stockPointCode.contains(t.getStockPointCodeTo()))
                        .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                bcInventoryShiftDetail.setAdjustQuantityFromFloat(adjustQuantityFromFloat);
//                BigDecimal endingInventory = endingInventoryForAdjust.add(adjustQuantityFromPortOfFreeStorage)
//                        .add(adjustQuantityFromPort).add(adjustQuantityFromFloat).add(bcInventoryShiftDetail.getUseReplaceQuantity().subtract(useReplaceQuantity));
                // 期末库存去掉免堆期的
                BigDecimal endingInventory = endingInventoryForAdjust.add(adjustQuantityFromPort)
                        .add(adjustQuantityFromFloat).add(bcInventoryShiftDetail.getUseReplaceQuantity().subtract(useReplaceQuantity));
                bcInventoryShiftDetail.setEndingInventory(endingInventory);
                factoryOpeningInventoryHasAdjust.put(productCode, bcInventoryShiftDetail.getEndingInventory());
                // 补货缺口
                BigDecimal safetyStockGap = bcInventoryShiftDetail.getSafetyStockLevelMin()
                        .subtract(bcInventoryShiftDetail.getEndingInventory());
                safetyStockGap = safetyStockGap.compareTo(BigDecimal.ZERO) > 0 ? safetyStockGap : BigDecimal.ZERO;
                bcInventoryShiftDetail.setSafetyStockGap(safetyStockGap);
//                bcInventoryShiftCreateList.add(bcInventoryShiftDetail);
            }
        }

        //更新原片替代的替代需求数量
        if (MapUtils.isNotEmpty(usedAsReplaceQuantityMap) && !whetherBreak){
            Set<String> keys = usedAsReplaceQuantityMap.keySet();
            List<String> productCodes = new ArrayList<>();
            for (String key : keys) {
                productCodes.add(key.split("#")[0]);
            }

            List<GlassInventoryShiftDataPO> shiftDataPOList = glassInventoryShiftDataCreateList.stream()
                    .filter(item -> productCodes.contains(item.getProductCode()))
                    .collect(Collectors.toList());

            // 有替代需求的物料需要重新推移
            transferToBc(mrpContextDTO, shiftDataPOList, factoryOpeningInventory, demandByProductAndDate,
                    portInventoryOccupyList, floatInventoryOccupyList, bcInventoryShiftCreateList,
                    usedAsReplaceQuantityMap, true);

        }
    }

    private GlassSubstitutionRelationshipVO createMainGlassSubstitutionRelationship(String mainProductCode){
        GlassSubstitutionRelationshipVO glassSubstitutionRelationshipVO = new GlassSubstitutionRelationshipVO();
        glassSubstitutionRelationshipVO.setSubstituteProductCode(mainProductCode);
        // 单耗设置为1
        glassSubstitutionRelationshipVO.setSubstituteInputFactor(BigDecimal.ONE);
        // 切裁率设置为最小
        glassSubstitutionRelationshipVO.setCuttingRate(BigDecimal.ZERO);
        return glassSubstitutionRelationshipVO;
    }

    private String getTransferProductByProductBom(String productCode, List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipVOList) {
        if (CollectionUtils.isEmpty(glassSubstitutionRelationshipVOList)) {
            return productCode;
        }
        // 获取生产BOM
        List<String> productionBomList = glassSubstitutionRelationshipVOList.stream()
                .map(GlassSubstitutionRelationshipVO::getProductionSubstituteProductCode)
                .filter(Objects::nonNull)  // 过滤掉null值
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productionBomList)) {
            return productCode;
        }
        return productionBomList.get(0);
    }

    private BigDecimal allocateGlassSubstitutionRelationship(BigDecimal adjustQuantity,
                                                             String inventoryShiftDateStr,
                                                             GlassInventoryShiftDetailPO inventoryShiftDetailPO,
                                                             Map<String, BigDecimal> factoryOpeningInventory,
                                                             List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipVOList,
                                                             Map<String, BigDecimal> usedAsReplaceQuantityMap) {
        // 获取原片的替代料数据
        if (CollectionUtils.isEmpty(glassSubstitutionRelationshipVOList)) {
            return adjustQuantity;
        }
        // 按照切裁率排序
        glassSubstitutionRelationshipVOList = glassSubstitutionRelationshipVOList.stream()
                .sorted(Comparator.comparing(
                        GlassSubstitutionRelationshipVO::getCuttingRate,
                        Comparator.nullsLast(Comparator.naturalOrder())
                ))
                .collect(Collectors.toList());
        Map<String, BigDecimal> substitutionMap = new HashMap<>();
        for (GlassSubstitutionRelationshipVO relationshipVO : glassSubstitutionRelationshipVOList) {
            substitutionMap.put(relationshipVO.getSubstituteProductCode(),
                    null == relationshipVO.getSubstituteInputFactor() ? BigDecimal.ONE : relationshipVO.getSubstituteInputFactor());
        }
        for (Map.Entry<String, BigDecimal> entry : substitutionMap.entrySet()) {
            String substituteProductCode = entry.getKey();
            // 替代料单耗
            BigDecimal substituteInputFactor = entry.getValue();
            // 获取替代料的本厂库存
            BigDecimal substituteProductInventory = factoryOpeningInventory.get(substituteProductCode);
            if (null == substituteProductInventory || substituteProductInventory.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            // 根据替代单耗计算的调整量数据
            BigDecimal adjustQuantityInputFactor = adjustQuantity.multiply(substituteInputFactor);

            if (substituteProductInventory.compareTo(adjustQuantityInputFactor) >= 0) {
                factoryOpeningInventory.put(substituteProductCode, substituteProductInventory.subtract(adjustQuantityInputFactor));
                // 计算推移原片被替代量
                inventoryShiftDetailPO.setUseReplaceQuantity(inventoryShiftDetailPO.getUseReplaceQuantity().add(adjustQuantity));
                usedAsReplaceQuantityMap.put(String.join("#", substituteProductCode, inventoryShiftDateStr), adjustQuantityInputFactor);
                adjustQuantity = BigDecimal.ZERO;
            } else {
                factoryOpeningInventory.put(substituteProductCode, BigDecimal.ZERO);
                // 计算推移原片被替代量
                usedAsReplaceQuantityMap.put(String.join("#", substituteProductCode, inventoryShiftDateStr), substituteProductInventory);
                substituteProductInventory = substituteProductInventory.divide(substituteInputFactor, RoundingMode.HALF_UP);
                inventoryShiftDetailPO.setUseReplaceQuantity(inventoryShiftDetailPO.getUseReplaceQuantity().add(substituteProductInventory));
                adjustQuantity = adjustQuantity.subtract(substituteProductInventory);
            }
        }
        return adjustQuantity;
    }

    private static BigDecimal allocateInventory(MrpContextDTO mrpContextDTO,
                                                String productCode,
                                                GlassInventoryShiftDetailPO inventoryShift,
                                                List<MaterialPlanInventoryOccupyPO> inventoryOccupyList,
                                                BigDecimal adjustFromPort,
                                                Date inventoryShiftDate,
                                                String mrpSupplySource,
                                                boolean bcDestination,
                                                boolean whetherUseSubstitution,
                                                List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipVOList,
                                                Map<String, BigDecimal> usedAsReplaceQuantityMap) {
        MrpDemandDTO mrpDemandDTO = MrpDemandDTO.builder()
                .demandQuantity(adjustFromPort)
                .unFulfillmentQuantity(adjustFromPort)
                .demandTime(inventoryShiftDate)
                .demandSource(MrpDemandSourceEnum.FACTORY_SAFE_STOCK.getCode())
                .productCode(productCode)
                .build();
        AllocateDTO standardAllocateDTO = getStandardAllocateDTO(mrpSupplySource, mrpDemandDTO, mrpContextDTO, bcDestination,
                null, false, usedAsReplaceQuantityMap, whetherUseSubstitution);
        MrpAllocateResultDO standardPortAllocateResultDO = InventoryAllocator.allocate(standardAllocateDTO);
        standardPortAllocateResultDO.getMaterialPlanInventoryOccupyList().forEach(t -> t.setInventoryShiftDetailId(inventoryShift.getId()));
        inventoryOccupyList.addAll(standardPortAllocateResultDO.getMaterialPlanInventoryOccupyList());

        // 判断需求是否满足、满足则返回
        if (whetherUseSubstitution &&
                CollectionUtils.isNotEmpty(glassSubstitutionRelationshipVOList) &&
                mrpDemandDTO.getUnFulfillmentQuantity().compareTo(BigDecimal.ZERO) > 0) {
            AllocateDTO substituteAllocateDTO = getStandardAllocateDTO(mrpSupplySource, mrpDemandDTO, mrpContextDTO, bcDestination,
                    glassSubstitutionRelationshipVOList, true, usedAsReplaceQuantityMap, true);
            MrpAllocateResultDO substituteMrpAllocateResultDO = InventoryAllocator.allocate(substituteAllocateDTO);
            substituteMrpAllocateResultDO.getMaterialPlanInventoryOccupyList().forEach(t -> t.setInventoryShiftDetailId(inventoryShift.getId()));
            inventoryOccupyList.addAll(substituteMrpAllocateResultDO.getMaterialPlanInventoryOccupyList());
            // 计算推移原片被替代量
            inventoryShift.setUseReplaceQuantity(inventoryShift.getUseReplaceQuantity().add(substituteMrpAllocateResultDO.getUseReplaceQuantity()));
        }
        return mrpDemandDTO.getUnFulfillmentQuantity();
    }


    private void freeStorageTransport(String productCode,
                                      MrpContextDTO mrpContextDTO,
                                      Date inventoryShiftDate,
                                      List<Date> inventoryShiftDateList,
                                      GlassInventoryShiftDetailPO bcInventoryShift,
                                      List<MaterialPlanInventoryOccupyPO> portInventoryOccupyList) {
        List<MrpSupplyDTO> portInventory = mrpContextDTO.getPortSupplyList().stream()
                .filter(t -> MrpSupplySourceEnum.PORT_INVENTORY.getCode().equals(t.getSupplySource())).collect(Collectors.toList());
        Map<String, Integer> portFreeStorageMap = mrpContextDTO.getPortFreeStorageMap();
        Calendar cal = Calendar.getInstance();
        if (CollectionUtils.isEmpty(portInventory)) {
            return;
        }
        Set<String> containerNumberOfFreeStorage = new HashSet<>();
        for (MrpSupplyDTO mrpSupplyDTO : portInventory) {
            if (mrpSupplyDTO.getUnAllocatedQuantity().compareTo(mrpSupplyDTO.getSupplyQuantity()) < 0) {
                continue;
            }

            // 两天后过免堆期码头库存
            String key = String.join("&&", mrpSupplyDTO.getCarrierCode(), mrpSupplyDTO.getStockPointCode());
            // 获取不到免堆期
            if (MapUtils.isEmpty(portFreeStorageMap) || !portFreeStorageMap.containsKey(key)) {
                continue;
            }

            String routingKey = String.join("&&", mrpSupplyDTO.getStockPointCode(), mrpContextDTO.getBcStockPointCodeList().get(0));
            TransportRoutingVO transportRouting = mrpContextDTO.getTransportRoutingGroupMap().get(routingKey).get(0);

            cal.setTime(inventoryShiftDate);
            cal.add(Calendar.DAY_OF_YEAR, -transportRouting.getTransportDate().intValue());
            Date portInventoryDate = cal.getTime();
//                Date portInventoryDate = DateUtils.moveDay(inventoryShiftDate, -transportRouting.getTransportDate().intValue());
            if (portInventoryDate.before(inventoryShiftDateList.get(0))) {
                continue;
            }

            cal.setTime(DateUtils.getDayFirstTime(mrpSupplyDTO.getSupplyTime()));
            cal.add(Calendar.DAY_OF_YEAR, portFreeStorageMap.get(key));
            Date chargeDate = cal.getTime();
//                Date chargeDate = DateUtils.moveDay(DateUtils.getDayFirstTime(mrpSupplyDTO.getSupplyTime()),
//                        portFreeStorageMap.get(key));

            cal.setTime(inventoryShiftDate);
            cal.add(Calendar.DAY_OF_YEAR, 2);
            Date twoDaytransitionDate = cal.getTime();
            if (chargeDate.compareTo(twoDaytransitionDate) == 0) {
                containerNumberOfFreeStorage.add(mrpSupplyDTO.getContainerNumber());
            }
        }
        if (CollectionUtils.isNotEmpty(containerNumberOfFreeStorage)) {
            mtAllContainerTransfer(productCode, mrpContextDTO, inventoryShiftDate, bcInventoryShift, portInventoryOccupyList, portInventory, containerNumberOfFreeStorage);
        }
    }

    private static void mtAllContainerTransfer(String productCode,
                                               MrpContextDTO mrpContextDTO,
                                               Date inventoryShiftDate,
                                               GlassInventoryShiftDetailPO bcInventoryShift,
                                               List<MaterialPlanInventoryOccupyPO> portInventoryOccupyList,
                                               List<MrpSupplyDTO> portInventory,
                                               Set<String> containerNumber) {

        List<String> portInventoryIds = portInventoryOccupyList.stream().map(MaterialPlanInventoryOccupyPO::getInventoryId)
                .collect(Collectors.toList());
        Calendar cal = Calendar.getInstance();

        for (MrpSupplyDTO mrpSupplyDTO : portInventory) {
            if (!containerNumber.contains(mrpSupplyDTO.getContainerNumber()) || mrpSupplyDTO.getUnAllocatedQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
//            if (containerNumber.contains(mrpSupplyDTO.getContainerNumber())) {
                // 将码头库存供应设置为0
                String routingKey = String.join("&&", mrpSupplyDTO.getStockPointCode(), mrpContextDTO.getBcStockPointCodeList().get(0));
                TransportRoutingVO transportRouting = mrpContextDTO.getTransportRoutingGroupMap().get(routingKey).get(0);
                cal.setTime(inventoryShiftDate);
                cal.add(Calendar.DAY_OF_YEAR, -transportRouting.getTransportDate().intValue());
                Date portInventoryDate = cal.getTime();
//                Date portInventoryDate = DateUtils.moveDay(inventoryShiftDate, -transportRouting.getTransportDate().intValue());
                MaterialPlanInventoryOccupyPO portInventoryOccupy = new MaterialPlanInventoryOccupyPO();
                portInventoryOccupy.setInventoryShiftDetailId(bcInventoryShift.getId());
                portInventoryOccupy.setInventoryId(mrpSupplyDTO.getSupplyId());
                portInventoryOccupy.setOccupyDate(portInventoryDate);
                portInventoryOccupy.setTransferRoutingId(transportRouting.getId());
                portInventoryOccupy.setTransportDateStart(portInventoryDate);
                portInventoryOccupy.setTransportDateEnd(inventoryShiftDate);
                portInventoryOccupy.setStockPointCodeFrom(mrpSupplyDTO.getStockPointCode());
                portInventoryOccupy.setStockPointCodeTo(mrpContextDTO.getBcStockPointCodeList().get(0));
                portInventoryOccupy.setSupplyProductCode(mrpSupplyDTO.getOriginProductCode());
                portInventoryOccupy.setOccupyQuantity(mrpSupplyDTO.getUnAllocatedQuantity());
                portInventoryOccupy.setInventoryType(MrpSupplySourceEnum.PORT_INVENTORY.getCode());
                portInventoryOccupy.setOccupyType(MrpInventoryOccupyEnum.FREE_STORAGE_TRANSPORT_OCCUPY.getCode());
                portInventoryOccupy.setDemandProductCode(productCode);
                portInventoryOccupy.setContainerNumber(mrpSupplyDTO.getContainerNumber());
                mrpSupplyDTO.setUnAllocatedQuantity(BigDecimal.ZERO);
                portInventoryOccupyList.add(portInventoryOccupy);
//            }
        }
    }

    private void satisfyStandardDemandToPort(MrpContextDTO mrpContextDTO, List<GlassInventoryShiftDataPO> glassInventoryShiftDataCreateList,
                                             Map<String, BigDecimal> factoryPortOpeningInventory,
                                             Map<String, MaterialDayTotalDemandDTO> demandByProductAndDate,
                                             List<GlassInventoryShiftDetailPO> bcAndMtInventoryShiftCreateList) {
        List<Date> inventoryShiftDateList = getInventoryShiftDateList(mrpContextDTO.getMrpCalcDate(), 60);
        Map<String, List<GlassSubstitutionRelationshipVO>> glassSubstitutionRelationshipMapOfProductionCode = mrpContextDTO.getGlassSubstitutionRelationshipMapOfProductionCode();
        for (GlassInventoryShiftDataPO inventoryShiftData : glassInventoryShiftDataCreateList) {
            String productCode = inventoryShiftData.getProductCode();
            // 获取ERP-BOM(主料)
            List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipVOList = glassSubstitutionRelationshipMapOfProductionCode.get(productCode);
//            String mainProductCode = getMainProductCode(productCode, glassSubstitutionRelationshipVOList);
            for (Date inventoryShiftDate : inventoryShiftDateList) {
                // 创建推移明细数据
                GlassInventoryShiftDetailPO inventoryShiftDetailPO = new GlassInventoryShiftDetailPO();
                inventoryShiftDetailPO.setId(UUIDUtil.getUUID());
                inventoryShiftDetailPO.setInventoryShiftDataId(inventoryShiftData.getId());
                inventoryShiftDetailPO.setInventoryDate(inventoryShiftDate);
                bcAndMtInventoryShiftCreateList.add(inventoryShiftDetailPO);


                BigDecimal satisfyStandardDemand = BigDecimal.ZERO;
                // 获取码头本厂的期初库存
                BigDecimal openingInventory = factoryPortOpeningInventory.get(productCode) == null
                        ? BigDecimal.ZERO : factoryPortOpeningInventory.get(productCode);
                inventoryShiftDetailPO.setOpeningInventory(openingInventory);

                // 总需求
                String demandKey = String.join("&&", productCode, DateUtils.dateToString(inventoryShiftDate));
                MaterialDayTotalDemandDTO materialDaysDemandDO = demandByProductAndDate.get(demandKey);

                // 标准规格需求
                BigDecimal standardDemand = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getStandardDemand();
                if (null == materialDaysDemandDO || standardDemand.compareTo(BigDecimal.ZERO) <= 0){
                    continue;
                }

                // 期初 - 标准规格需求
                if (standardDemand.compareTo(openingInventory) <= 0){
                    // 码头本厂库存可以满足标准规格需求
                    satisfyStandardDemand = standardDemand;
                    // 扣减码头本厂期初库存
                    openingInventory = openingInventory.subtract(standardDemand);
                    factoryPortOpeningInventory.put(productCode, openingInventory);
                }else if (standardDemand.compareTo(openingInventory) > 0){
                    // 码头本厂库存不能满足标准规格需求
                    satisfyStandardDemand = openingInventory;
                    factoryPortOpeningInventory.put(productCode, BigDecimal.ZERO);
                }
                materialDaysDemandDO.setSatisfyStandardDemand(satisfyStandardDemand);
            }
        }
    }

    protected void transferToPort(MrpContextDTO mrpContextDTO,
                                  List<GlassInventoryShiftDataPO> glassInventoryShiftDataCreateList,
                                  Map<String, BigDecimal> floatOpeningInventory,
                                  Map<String, BigDecimal> factoryPortOpeningInventory,
                                  Map<String, MaterialDayTotalDemandDTO> demandByProductAndDate,
                                  List<MaterialPlanInventoryOccupyPO> floatInventoryOccupyList,
                                  List<GlassInventoryShiftDetailPO> bcMtInventoryShiftCreateList,
                                  Map<String, BigDecimal> usedAsReplaceQuantityMap,
                                  boolean whetherBreak) {
        // 提取常量字符串，避免重复创建（如果频繁调用）
        final String SUPPLY_SOURCE_PREFIX = "FACTORY_ON_WAY";
        Map<String, GlassInventoryShiftDetailVO> fixedInventoryShift = new HashMap<>();
        if (!mrpContextDTO.isBcAdjust() && mrpContextDTO.getAdjustInventoryShiftDetail() != null) {
            fixedInventoryShift = mrpContextDTO.getMtGlassInventoryShiftDetailList().stream()
                    .filter(t -> !t.getInventoryDate().after(mrpContextDTO.getAdjustInventoryShiftDetail().getInventoryDate()))
                    .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getInventoryDate()), Function.identity()));
        }
        Map<String, GlassInventoryShiftDetailPO> inventoryShiftDetailPOMap = bcMtInventoryShiftCreateList.stream()
                .collect(Collectors.toMap(item -> String.join("#", item.getInventoryShiftDataId(), DateUtils.dateToString(item.getInventoryDate())),
                        Function.identity()));

        Map<String, List<GlassSubstitutionRelationshipVO>> glassSubstitutionRelationshipMap = mrpContextDTO.getGlassSubstitutionRelationshipMapOfProductionCode();

        Map<String, BigDecimal> factoryPortOpeningInventoryHasAdjust = new HashMap<>(factoryPortOpeningInventory);
        // 库存推移日期范围
        List<Date> inventoryShiftDateList = getInventoryShiftDateList(mrpContextDTO.getMrpCalcDate(), 60);

        for (GlassInventoryShiftDataPO glassInventoryShiftData : glassInventoryShiftDataCreateList) {
            String productCode = glassInventoryShiftData.getProductCode();
            List<String> mixSubstitutionList;
            if (mrpContextDTO.getMixSubstitution().containsKey(productCode)){
                mixSubstitutionList = mrpContextDTO.getMixSubstitution().get(productCode);
            }else {
                mixSubstitutionList = new ArrayList<>();
                mixSubstitutionList.add(productCode);
            }
            // 获取原片替代映射数据
            List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipList = glassSubstitutionRelationshipMap.get(productCode);
            // 获取ERP-BOM(主料)
            String mainProductCode = getMainProductCode(productCode, glassSubstitutionRelationshipList);
            // 获取主料(ERP-BOM)是否禁用
            boolean rawProductInventoryDisabledFlag = getRawProductInventoryDisabledFlag(glassSubstitutionRelationshipList);
            if (!productCode.equals(mainProductCode) && !rawProductInventoryDisabledFlag){
                // 创建主料替代
                GlassSubstitutionRelationshipVO mainGlassSubstitutionRelationship = createMainGlassSubstitutionRelationship(mainProductCode);
                glassSubstitutionRelationshipList.add(mainGlassSubstitutionRelationship);
            }
            // 物料所有浮法厂的期初库存
            BigDecimal allFloatOpeningInventory = floatOpeningInventory.entrySet().stream()
                    .filter(entry -> entry.getKey().contains(productCode))
                    .map(Map.Entry::getValue)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            for (int i = 0; i < inventoryShiftDateList.size(); i++) {
                Date inventoryShiftDate = inventoryShiftDateList.get(i);
                String inventoryShiftDateStr = DateUtils.dateToString(inventoryShiftDate);

                // 预计算目标时间：将 inventoryShiftDate 加 1 天，避免循环内重复计算
                Date targetSupplyTime = DateUtils.moveDay(inventoryShiftDate, 1);
                GlassInventoryShiftDetailPO bcMtInventoryShift = inventoryShiftDetailPOMap.get(String.join("#", glassInventoryShiftData.getId(), inventoryShiftDateStr));

//                GlassInventoryShiftDetailPO bcMtInventoryShift = new GlassInventoryShiftDetailPO();
                List<MaterialPlanInventoryOccupyPO> floatInventoryOccupyTempList = new ArrayList<>(2048);
                bcMtInventoryShift.setId(UUIDUtil.getUUID());
                bcMtInventoryShift.setInventoryShiftDataId(glassInventoryShiftData.getId());
                bcMtInventoryShift.setAllFfInventory(allFloatOpeningInventory);
                bcMtInventoryShift.setInventoryDate(inventoryShiftDate);
                List<String> stockPointCodeList = Lists.newArrayList();
                stockPointCodeList.addAll(mrpContextDTO.getBcStockPointCodeList());
                stockPointCodeList.addAll(mrpContextDTO.getPortStockPointCodeList());
                bcMtInventoryShift.setStockPointCode(String.join("&&", stockPointCodeList));
                bcMtInventoryShift.setStockPointType(MrpStockPointTypeEnum.BCMT.getCode());
                // 码头本厂期初库存
                BigDecimal openingInventory;
                if (i == 0){
                    openingInventory = bcMtInventoryShift.getOpeningInventory();
                }else {
                    openingInventory = factoryPortOpeningInventoryHasAdjust.get(productCode) == null
                            ? BigDecimal.ZERO : factoryPortOpeningInventoryHasAdjust.get(productCode);
                }

                // 总需求
                String demandKey = String.join("&&", productCode, DateUtils.dateToString(inventoryShiftDate));
                MaterialDayTotalDemandDTO materialDaysDemandDO = demandByProductAndDate.get(demandKey);
                // 标准规格需求
                BigDecimal standardDemand = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getStandardDemand();
                bcMtInventoryShift.setDemandQuantity(standardDemand);
                // 已满足的标准规格需求
                BigDecimal satisfyStandardDemand = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getSatisfyStandardDemand();
                bcMtInventoryShift.setOpeningInventory(openingInventory);
                // 标准规格需求-已满足的标准规格需求
                standardDemand = standardDemand.subtract(satisfyStandardDemand);

                // 替代规格需求
                BigDecimal usedAsReplaceQuantity = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getUsedAsReplaceQuantity();
                String joinKey = String.join("#", productCode, inventoryShiftDateStr);
                // 考虑二次计算再加上替代数量
                if (usedAsReplaceQuantityMap.containsKey(joinKey) && whetherBreak){
                    usedAsReplaceQuantity = usedAsReplaceQuantity.add(usedAsReplaceQuantityMap.get(joinKey));
                }
                bcMtInventoryShift.setUsedAsReplaceQuantity(usedAsReplaceQuantity);
                // 被替代的量
                BigDecimal useReplaceQuantity = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getUseReplaceQuantity();
                bcMtInventoryShift.setUseReplaceQuantity(useReplaceQuantity);
                // 总需求 = 标准规格需求 + 替代规格需求 - 被替代的量 + 已满足标准规格需求
                BigDecimal totalDemand = standardDemand.add(usedAsReplaceQuantity).subtract(useReplaceQuantity).add(satisfyStandardDemand);
                // 最小安全库存天数
                BigDecimal safetyStockDaysMin = glassInventoryShiftData.getSafetyStockDaysMin().add(glassInventoryShiftData.getPortInventoryDays());
                bcMtInventoryShift.setSafetyStockLevelMin(safetyStockDaysMin);
                // 最小完全库存水位
                BigDecimal safetyStockLevelMin = getInventoryLevel(safetyStockDaysMin, productCode, inventoryShiftDate, demandByProductAndDate);
                bcMtInventoryShift.setSafetyStockLevelMin(safetyStockLevelMin);
                // 目标安全库存天数
                BigDecimal safetyStockDaysStandard = glassInventoryShiftData.getSafetyStockDaysStandard().add(glassInventoryShiftData.getPortInventoryDays());
                // 目标安全库存水位
                BigDecimal safetyStockLevelStandard = getInventoryLevel(safetyStockDaysStandard, productCode, inventoryShiftDate, demandByProductAndDate);
                bcMtInventoryShift.setSafetyStockLevelStandard(safetyStockLevelStandard);
                // 最大安全库存天数
                BigDecimal safetyStockDaysMax = glassInventoryShiftData.getSafetyStockDaysMax().add(glassInventoryShiftData.getPortInventoryDays());
                // 最大完全库存水位
                BigDecimal safetyStockLevelMax = getInventoryLevel(safetyStockDaysMax, productCode, inventoryShiftDate, demandByProductAndDate);
                bcMtInventoryShift.setSafetyStockLevelMax(safetyStockLevelMax);
                // 计划调拨浮法汽运
                BigDecimal adjustQuantityToBc = floatInventoryOccupyList.parallelStream()
                        .filter(t -> mixSubstitutionList.contains(t.getSupplyProductCode())
                                && t.getTransportDateEnd().compareTo(inventoryShiftDate) == 0
                                && mrpContextDTO.getBcStockPointCodeList().contains(t.getStockPointCodeTo()))
                        .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 本厂在途
                List<MrpSupplyDTO> onRoadList = mrpContextDTO.getFactorySupplyList().stream()
                        .filter(t ->
                                // 调整条件顺序：先执行轻量级条件，减少后续耗时操作
                                t.getSupplyTime().compareTo(targetSupplyTime) == 0 &&
                                        t.getProductCode().equals(productCode) &&
                                        t.getSupplySource().startsWith(SUPPLY_SOURCE_PREFIX)

                        )
                        .collect(Collectors.toList());

                BigDecimal factoryOnRoadQuantity = onRoadList.stream().map(MrpSupplyDTO::getSupplyQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 码头在途
                List<MrpSupplyDTO> portOnRoadList = mrpContextDTO.getPortSupplyList().parallelStream()
                        .filter(t -> t.getSupplyTime().compareTo(targetSupplyTime) == 0 &&
                                t.getProductCode().equals(productCode) &&
                                t.getSupplySource().equals(MrpSupplySourceEnum.PORT_ON_WAY.getCode()))
                        .collect(Collectors.toList());

                BigDecimal portOnRoadQuantity = portOnRoadList.stream().map(MrpSupplyDTO::getSupplyQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal onRoadQuantity = portOnRoadQuantity.add(factoryOnRoadQuantity);
                bcMtInventoryShift.setTransitQuantityFromPort(onRoadQuantity);
                // 浮法计划送柜量(码头本厂的计划到柜量)
                BigDecimal planInputQuantity = mrpContextDTO.getMaterialPlanTransferList().stream()
                        .filter(t -> mrpContextDTO.getFloatStockPointCodeList().contains(t.getStockPointCodeFrom())
                                && mixSubstitutionList.contains(t.getProductCode())
                                && t.getTransferDateArrive().compareTo(inventoryShiftDate) == 0)
                        .map(MaterialPlanTransferVO::getTransferQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);

                // 调整前期末库存 = 期初库存 + 在途 + 计划送柜量 + 计划调拨浮法汽运 - 总需求
                BigDecimal endingInventoryBeforeAdjust = openingInventory.add(onRoadQuantity).add(planInputQuantity)
                        .subtract(totalDemand);
                factoryPortOpeningInventory.put(productCode, endingInventoryBeforeAdjust);

                // 期初库存包含前一天调整量
//                BigDecimal openingInventoryHasAdjust = factoryPortOpeningInventoryHasAdjust.get(productCode) == null ?
//                        BigDecimal.ZERO : factoryPortOpeningInventoryHasAdjust.get(productCode);
                // 用于计算调拨量的期末库存
                BigDecimal endingInventoryForAdjust = openingInventory.add(onRoadQuantity).add(planInputQuantity)
                        .add(adjustQuantityToBc).subtract(totalDemand);

                GlassInventoryShiftDetailVO materialPlanInventoryShiftVO = fixedInventoryShift.get(inventoryShiftDateStr);
                if (Objects.nonNull(materialPlanInventoryShiftVO)) {
                    BigDecimal adjustQuantity = materialPlanInventoryShiftVO.getAdjustQuantityFromFloat() == null
                            ? BigDecimal.ZERO : materialPlanInventoryShiftVO.getAdjustQuantityFromFloat();
                    BigDecimal unFulfillmentQuantity = allocateInventory(mrpContextDTO, productCode, bcMtInventoryShift,
                            floatInventoryOccupyTempList, adjustQuantity, inventoryShiftDate,
                            MrpSupplySourceEnum.FLOAT_INVENTORY.getCode(), false,
                            false, null, usedAsReplaceQuantityMap);
                    if (unFulfillmentQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        throw new BusinessException("浮法库存不足或运输路时长无法满足");
                    }
                } else {
                    // 期望的调整量
                    BigDecimal adjustQuantity = getAdjustQuantity(endingInventoryForAdjust, safetyStockLevelMin,
                            safetyStockLevelStandard, safetyStockLevelMax);
                    if (adjustQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        // 先用原片替代映射的替代料先去满足
                        adjustQuantity = allocateGlassSubstitutionRelationship(adjustQuantity, inventoryShiftDateStr, bcMtInventoryShift,
                                factoryPortOpeningInventory, glassSubstitutionRelationshipList, usedAsReplaceQuantityMap);
                    }
                    if (adjustQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        // 浮法调拨
                        allocateInventory(mrpContextDTO, productCode, bcMtInventoryShift, floatInventoryOccupyTempList,
                                adjustQuantity, inventoryShiftDate, MrpSupplySourceEnum.FLOAT_INVENTORY.getCode(), false,
                                false, glassSubstitutionRelationshipList, usedAsReplaceQuantityMap);
                    }
                }
                // 获取调拨后的替代数据, TODO需要减去指定替代的数据
                BigDecimal useReplaceQuantityAfter = bcMtInventoryShift.getUseReplaceQuantity();
//                BigDecimal useReplaceQuantityAfter = bcMtInventoryShift.getUseReplaceQuantity().subtract(useReplaceQuantity);
                //期末库存需要加上替代数据
                endingInventoryForAdjust = endingInventoryForAdjust.add(useReplaceQuantityAfter);

                // 决策运入数量
                BigDecimal newInputQuantity = floatInventoryOccupyTempList.stream()
                        .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                bcMtInventoryShift.setAdjustQuantityFromFloat(newInputQuantity);
                bcMtInventoryShift.setEndingInventory(endingInventoryForAdjust.add(newInputQuantity));
                // 补货缺口
                BigDecimal safetyStockGap = safetyStockLevelMin
                        .subtract(bcMtInventoryShift.getEndingInventory());
                safetyStockGap = safetyStockGap.compareTo(BigDecimal.ZERO) > 0 ? safetyStockGap : BigDecimal.ZERO;
                bcMtInventoryShift.setSafetyStockGap(safetyStockGap);
//                bcMtInventoryShiftCreateList.add(bcMtInventoryShift);
                if (CollectionUtils.isNotEmpty(floatInventoryOccupyTempList)) {
                    floatInventoryOccupyList.addAll(floatInventoryOccupyTempList);
                }
                factoryPortOpeningInventoryHasAdjust.put(productCode, bcMtInventoryShift.getEndingInventory());
            }
        }

        // 更新原片替代的替代需求数量
        if (MapUtils.isNotEmpty(usedAsReplaceQuantityMap) && !whetherBreak){
            Set<String> keys = usedAsReplaceQuantityMap.keySet();
            List<String> productCodes = new ArrayList<>();
            for (String key : keys) {
                productCodes.add(key.split("#")[0]);
            }

            List<GlassInventoryShiftDataPO> shiftDataPOList = glassInventoryShiftDataCreateList.stream()
                    .filter(item -> productCodes.contains(item.getProductCode()))
                    .collect(Collectors.toList());

            transferToPort(mrpContextDTO, shiftDataPOList, floatOpeningInventory, factoryPortOpeningInventory,
                    demandByProductAndDate, floatInventoryOccupyList, bcMtInventoryShiftCreateList,
                    usedAsReplaceQuantityMap, true);
        }
    }

    private String getMainProductCode(String productCode, List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipList) {
        if (CollectionUtils.isEmpty(glassSubstitutionRelationshipList)) {
            return productCode;
        }
        // 获取生产BOM
        List<String> productionBomList = glassSubstitutionRelationshipList.stream()
                .map(GlassSubstitutionRelationshipVO::getRawProductCode)
                .filter(Objects::nonNull)  // 过滤掉null值
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productionBomList)) {
            return productCode;
        }
        return productionBomList.get(0);
    }

    private boolean getRawProductInventoryDisabledFlag(List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipList) {
        if (CollectionUtils.isEmpty(glassSubstitutionRelationshipList)) {
            return false;
        }
        // 获取被禁用的主料数据
        List<GlassSubstitutionRelationshipVO> filterList = glassSubstitutionRelationshipList.stream()
                .filter(item -> org.apache.commons.lang3.StringUtils.equals(YesOrNoEnum.YES.getCode(), item.getRawProductInventoryDisabled()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filterList)){
            return true;
        }
        return false;
    }

    protected static BigDecimal getAdjustQuantity(BigDecimal endingInventory,
                                                  BigDecimal safetyStockLevelMin,
                                                  BigDecimal safetyStockLevelStandard,
                                                  BigDecimal safetyStockLevelMax) {
        // 期末库存小于最小安全库存，则取最小安全库存
        if (endingInventory.compareTo(safetyStockLevelMin) < 0) {
            return safetyStockLevelStandard.subtract(endingInventory);
        }
        return BigDecimal.ZERO;
    }

    private static BigDecimal getInventoryLevel(BigDecimal standardStockDay,
                                                String productCode,
                                                Date currentDate,
                                                Map<String, MaterialDayTotalDemandDTO> mrpDemandOfDate) {
        BigDecimal standardStockDayInt = (standardStockDay == null) ? BigDecimal.ZERO : standardStockDay;
        BigDecimal result = BigDecimal.ZERO;
        if (standardStockDayInt.compareTo(BigDecimal.ZERO) == 0) {
            return result;
        }
        if (mrpDemandOfDate == null || mrpDemandOfDate.isEmpty()) {
            return result;
        }
        List<BigDecimal> bigDecimals = splitBigDecimal(standardStockDayInt);
        for (int i = 1; i <= bigDecimals.size(); i++) {
            Date dateAfter = DateUtils.moveDay(currentDate, i);
            String key = String.join("&&", productCode, DateUtils.dateToString(dateAfter));
            MaterialDayTotalDemandDTO materialDayTotalDemandDTO = mrpDemandOfDate.get(key);
            if (null == materialDayTotalDemandDTO) {
                continue;
            }
            // 总需求 = 标准需求 + 使用替代量 - 使用替代量
            BigDecimal demandQty = materialDayTotalDemandDTO.getStandardDemand()
                    .add(materialDayTotalDemandDTO.getUsedAsReplaceQuantity())
                    .subtract(materialDayTotalDemandDTO.getUseReplaceQuantity());
            result = result.add(bigDecimals.get(i - 1).multiply(demandQty).setScale(0, RoundingMode.CEILING));
        }
        return result;
    }

    private static List<BigDecimal> splitBigDecimal(BigDecimal bigDecimal) {
        List<BigDecimal> parts = new ArrayList<>();
        BigDecimal remainder = bigDecimal;
        while (remainder.compareTo(BigDecimal.ONE) >= 0) {
            parts.add(BigDecimal.ONE);
            remainder = remainder.subtract(BigDecimal.ONE);
        }
        if (remainder.compareTo(BigDecimal.ZERO) > 0) {
            parts.add(remainder);
        }
        return parts;
    }

    protected static AllocateDTO getStandardAllocateDTO(String inventoryType,
                                                        MrpDemandDTO mrpDemand,
                                                        MrpContextDTO mrpContextDTO,
                                                        boolean bcDestination,
                                                        List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipVOList,
                                                        boolean substitutionFlag,
                                                        Map<String, BigDecimal> usedAsReplaceQuantityMap,
                                                        boolean whetherUseSubstitution
    ) {
        // 码头标准规格分配
        AllocateDTO allocateDTO = new AllocateDTO();
        allocateDTO.setBcDestination(bcDestination);
        allocateDTO.setMixSubstitution(mrpContextDTO.getMixSubstitution());
        allocateDTO.setWhetherUseSubstitution(whetherUseSubstitution);
        List<String> demandProductCodeList;
        // 使用替代料供应
        Map<String, BigDecimal> substituteProductInputFactorMap = new HashMap<>();
        if (substitutionFlag) {
            // 替代料物品
            demandProductCodeList = glassSubstitutionRelationshipVOList.stream()
                    .map(GlassSubstitutionRelationshipVO::getSubstituteProductCode)
                    .distinct().collect(Collectors.toList());
            for (GlassSubstitutionRelationshipVO item : glassSubstitutionRelationshipVOList) {
                substituteProductInputFactorMap.put(item.getSubstituteProductCode(),
                        null == item.getSubstituteInputFactor() ? BigDecimal.ONE : item.getSubstituteInputFactor());
            }
        } else {
            // 添加自己的默认单耗1
            substituteProductInputFactorMap.put(mrpDemand.getProductCode(), BigDecimal.ONE);
            demandProductCodeList = new ArrayList<>();
            demandProductCodeList.add(mrpDemand.getProductCode());
        }

        // 浮法调拨需要使用生产单耗
        if (MrpSupplySourceEnum.FLOAT_INVENTORY.getCode().equals(inventoryType) && CollectionUtils.isNotEmpty(glassSubstitutionRelationshipVOList)){
            List<GlassSubstitutionRelationshipVO> filterList = glassSubstitutionRelationshipVOList.stream()
                    .filter(item -> org.apache.commons.lang3.StringUtils.isNotBlank(item.getProductionSubstituteProductCode()) && null != item.getProductionInputFactor())
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterList)){
                substituteProductInputFactorMap.put(filterList.get(0).getProductionSubstituteProductCode(),
                        null == filterList.get(0).getProductionInputFactor() ? BigDecimal.ONE : filterList.get(0).getProductionInputFactor());
            }
        }

        if (MrpSupplySourceEnum.FACTORY_INVENTORY.getCode().equals(inventoryType)) {
            allocateDTO.setMrpSupplyAllList(mrpContextDTO.getFactorySupplyList());
            allocateDTO.setMrpSupplyList(mrpContextDTO.getFactorySupplyList()
                    .stream().filter(t -> demandProductCodeList.contains(t.getProductCode()))
                    .collect(Collectors.toList()));
        } else if (MrpSupplySourceEnum.PORT_INVENTORY.getCode().equals(inventoryType)) {
            allocateDTO.setMrpSupplyAllList(mrpContextDTO.getPortSupplyList());
            allocateDTO.setMrpSupplyList(mrpContextDTO.getPortSupplyList()
                    .stream().filter(t -> demandProductCodeList.contains(t.getProductCode()))
                    .collect(Collectors.toList()));
            allocateDTO.setSupplyStockPointType(StockPointTypeEnum.MT.getCode());
        } else if (MrpSupplySourceEnum.FLOAT_INVENTORY.getCode().equals(inventoryType)) {
            allocateDTO.setMrpSupplyAllList(mrpContextDTO.getFloatSupplyList());
            allocateDTO.setMrpSupplyList(mrpContextDTO.getFloatSupplyList()
                    .stream().filter(t -> demandProductCodeList.contains(t.getProductCode()))
                    .collect(Collectors.toList()));
            allocateDTO.setSupplyStockPointType(StockPointTypeEnum.FF.getCode());
        }

        mrpDemand.setDemandProductCodeList(demandProductCodeList);
        allocateDTO.setMrpDemand(mrpDemand);
        if (!MrpSupplySourceEnum.FACTORY_INVENTORY.getCode().equals(inventoryType)) {
            // 运输路径
            allocateDTO.setTransportRoutingList(mrpContextDTO.getTransportRoutingList());
        }
        allocateDTO.setMrpCalDateStart(mrpContextDTO.getMrpCalcDate());
        allocateDTO.setDemandGroupByProduct(mrpContextDTO.getDemandGroupByProduct());
        allocateDTO.setUsedAsReplaceQuantityMap(usedAsReplaceQuantityMap);
        allocateDTO.setSubstituteProductInputFactorMap(substituteProductInputFactorMap);
        return allocateDTO;
    }

    protected static Map<String, BigDecimal> getOpeningInventory(MrpContextDTO mrpContextDTO, List<String> supplySourceList) {

        List<MrpSupplyDTO> supplyList = Lists.newArrayList();
        for (String supplySource : supplySourceList) {
            if (MrpSupplySourceEnum.FACTORY_INVENTORY.getCode().equals(supplySource)) {
                for (MrpSupplyDTO mrpSupplyDTO : mrpContextDTO.getFactorySupplyList()) {
                    if (supplySource.equals(mrpSupplyDTO.getSupplySource())) {
                        supplyList.add(mrpSupplyDTO);
                    }
                }
            } else if (MrpSupplySourceEnum.PORT_INVENTORY.getCode().equals(supplySource)) {
                for (MrpSupplyDTO mrpSupplyDTO : mrpContextDTO.getPortSupplyList()) {
                    if (supplySource.equals(mrpSupplyDTO.getSupplySource())) {
                        supplyList.add(mrpSupplyDTO);
                    }
                }
            } else if (MrpSupplySourceEnum.FLOAT_INVENTORY.getCode().equals(supplySource)) {
                supplyList.addAll(mrpContextDTO.getFloatSupplyList());
            }
        }
        if (supplySourceList.size() > 1) {
            return supplyList.stream().collect(Collectors.groupingBy(MrpSupplyDTO::getProductCode,
                    Collectors.reducing(BigDecimal.ZERO, MrpSupplyDTO::getUnAllocatedQuantity, BigDecimal::add)));
        }
        if (supplySourceList.size() == 1 && supplySourceList.contains(MrpSupplySourceEnum.PORT_INVENTORY.getCode())) {
            return supplyList.stream().collect(Collectors.groupingBy(MrpSupplyDTO::getProductCode,
                    Collectors.reducing(BigDecimal.ZERO, MrpSupplyDTO::getUnAllocatedQuantity, BigDecimal::add)));
        }
        if (supplySourceList.size() == 1 && supplySourceList.contains(MrpSupplySourceEnum.FACTORY_INVENTORY.getCode())) {
            return supplyList.stream().collect(Collectors.groupingBy(MrpSupplyDTO::getProductCode,
                    Collectors.reducing(BigDecimal.ZERO, MrpSupplyDTO::getUnAllocatedQuantity, BigDecimal::add)));
        }
        return supplyList.stream().collect(Collectors.groupingBy(t -> t.getProductCode() + "&&" + t.getStockPointCode(),
                Collectors.reducing(BigDecimal.ZERO, MrpSupplyDTO::getUnAllocatedQuantity, BigDecimal::add)));
    }


    protected abstract Set<String> getInventoryShiftProductCodes(MrpContextDTO mrpContextDTO);


    @Override
    @BusinessMonitorLog(businessCode = "原片物料缺口计算", moduleCode = "MRP", businessFrequency = "DAY")
    protected MrpResultPO doMrpCalc(MrpContextDTO mrpContextDTO) {
        StopWatch stopWatch = new StopWatch("原片推移核心逻辑执行");
        MrpResultPO mrpResultPO = new MrpResultPO();

        // mrpDemandList过滤出是物料类型是原片的需求
        stopWatch.start("过滤出原片需求");
        Map<String, MaterialDayTotalDemandDTO> demandByProductAndDate = getMaterialDayTotalDemand(mrpContextDTO);
        Map<String, List<MaterialDayTotalDemandDTO>> demandGroupByProduct = getDemandGroupByProduct(mrpContextDTO);
        mrpContextDTO.setDemandGroupByProduct(demandGroupByProduct);

        List<GlassInventoryShiftDetailPO> glassInventoryShiftDetailCreateList = new ArrayList<>(1024 * 1024);
        // 码头库存批次占用
        List<MaterialPlanInventoryOccupyPO> inventoryOccupyCreateList = new ArrayList<>(1024 * 1024);
        // 物料替代计划
        List<MaterialPlanReplacePO> materialPlanReplaceCreateList = new ArrayList<>(1024 * 1024);
        stopWatch.stop();
        // 本厂码头期初库存
        stopWatch.start("本厂码头期初库存");
        Map<String, BigDecimal> factoryPortOpeningInventoryMap = getOpeningInventory(mrpContextDTO,
                Lists.newArrayList(MrpSupplySourceEnum.FACTORY_INVENTORY.getCode(), MrpSupplySourceEnum.PORT_INVENTORY.getCode()));
        stopWatch.stop();
        // 本厂期初库存
        stopWatch.start("本厂期初库存");
        Map<String, BigDecimal> factoryOpeningInventory = getOpeningInventory(mrpContextDTO, Lists.newArrayList(MrpSupplySourceEnum.FACTORY_INVENTORY.getCode()));
        stopWatch.stop();
        // 码头期初库存
        stopWatch.start("码头期初库存");
        Map<String, BigDecimal> portOpeningInventory = getOpeningInventory(mrpContextDTO, Lists.newArrayList(MrpSupplySourceEnum.PORT_INVENTORY.getCode()));
        stopWatch.stop();
        // 浮法期初库存
        stopWatch.start("浮法期初库存");
        Map<String, BigDecimal> floatOpeningInventory = getOpeningInventory(mrpContextDTO, Lists.newArrayList(MrpSupplySourceEnum.FLOAT_INVENTORY.getCode()));
        stopWatch.stop();
        // 获取需要库存推移的物料编码
        stopWatch.start("获取需要库存推移的物料编码");
        Set<String> inventoryShiftProductCodes = getInventoryShiftProductCodes(mrpContextDTO);
        stopWatch.stop();
        // 库存推移
        stopWatch.start("组装Data主表数据");
        List<GlassInventoryShiftDataPO> glassInventoryShiftDataCreateList = createGlassInventoryShiftDataList(mrpContextDTO,
                inventoryShiftProductCodes, mrpResultPO);
        stopWatch.stop();
        // 推移Data数据排序
        stopWatch.start("排序Data主表数据");
        glassInventoryShiftDataCreateList = sortInventoryShiftData(glassInventoryShiftDataCreateList, mrpContextDTO);
        stopWatch.stop();

        // 本厂库存推移：码头汽运和浮法汽运
        List<MaterialPlanInventoryOccupyPO> portOccupyList = new ArrayList<>();
        List<MaterialPlanInventoryOccupyPO> floatOccupyList = new ArrayList<>();

        List<GlassInventoryShiftDetailPO> bcInventoryShiftCreateList = new ArrayList<>(1024 * 10);
        // 记录替代数量,key=物料编码#日期
        Map<String, BigDecimal> bcUsedAsReplaceQuantityMap = new HashMap<>();
        Map<String, BigDecimal> portUsedAsReplaceQuantityMap = new HashMap<>();

        // 满足本厂标准规格需求
        stopWatch.start("满足本厂标准规格需求");
        satisfyStandardDemandToBc(mrpContextDTO, glassInventoryShiftDataCreateList, factoryOpeningInventory, demandByProductAndDate,
                portOccupyList, bcInventoryShiftCreateList);
        stopWatch.stop();

        stopWatch.start("本厂库存推移");
        transferToBc(mrpContextDTO, glassInventoryShiftDataCreateList, factoryOpeningInventory, demandByProductAndDate,
                portOccupyList, floatOccupyList, bcInventoryShiftCreateList, bcUsedAsReplaceQuantityMap, false);
        stopWatch.stop();

        // 满足本厂码头标准规格需求
        stopWatch.start("满足本厂码头标准规格需求");
        List<GlassInventoryShiftDetailPO> bcAndMtInventoryShiftCreateList = new ArrayList<>(1024 * 20);
        satisfyStandardDemandToPort(mrpContextDTO, glassInventoryShiftDataCreateList, factoryPortOpeningInventoryMap,
                demandByProductAndDate, bcAndMtInventoryShiftCreateList);
        stopWatch.stop();

        // 本厂码头库存推移：浮法海运调拨
        stopWatch.start("本厂码头库存推移");
        transferToPort(mrpContextDTO, glassInventoryShiftDataCreateList, floatOpeningInventory, factoryPortOpeningInventoryMap,
                demandByProductAndDate, floatOccupyList, bcAndMtInventoryShiftCreateList, portUsedAsReplaceQuantityMap, false);
        stopWatch.stop();

        // 生成浮法库存推移数据
        stopWatch.start("生成浮法库存推移数据");
        List<GlassInventoryShiftDetailPO> floatInventoryShiftCreateList = new ArrayList<>(bcAndMtInventoryShiftCreateList.size());
        createFloatInventoryShift(mrpContextDTO, glassInventoryShiftDataCreateList, floatOccupyList, floatOpeningInventory,
                floatInventoryShiftCreateList);
        stopWatch.stop();

        // 码头的库存推移数据
        stopWatch.start("码头的库存推移数据");
        List<GlassInventoryShiftDetailPO> portInventoryShiftCreateList = new ArrayList<>(bcAndMtInventoryShiftCreateList.size());
        createPortInventoryShift(mrpContextDTO, glassInventoryShiftDataCreateList, floatOccupyList, portOccupyList,
                portOpeningInventory, portInventoryShiftCreateList);
        stopWatch.stop();

        inventoryOccupyCreateList.addAll(floatOccupyList);
        inventoryOccupyCreateList.addAll(portOccupyList);
        glassInventoryShiftDetailCreateList.addAll(bcAndMtInventoryShiftCreateList);
        glassInventoryShiftDetailCreateList.addAll(floatInventoryShiftCreateList);
        glassInventoryShiftDetailCreateList.addAll(portInventoryShiftCreateList);
        glassInventoryShiftDetailCreateList.addAll(bcInventoryShiftCreateList);
        mrpResultPO.setPlanUserProductCodeList(mrpContextDTO.getPlanUserProductCodeList());
        mrpResultPO.setGlassInventoryShiftDataList(glassInventoryShiftDataCreateList);
        mrpResultPO.setGlassInventoryShiftDetailList(glassInventoryShiftDetailCreateList);
        // 生成替代计划
        stopWatch.start("生成替代计划");
        assemblyMaterialReplace(mrpContextDTO, glassInventoryShiftDataCreateList, glassInventoryShiftDetailCreateList,
                materialPlanReplaceCreateList);
        stopWatch.stop();
        mrpResultPO.setReplacePOList(materialPlanReplaceCreateList);
        mrpResultPO.setMaterialPlanOccupyPOList(inventoryOccupyCreateList);
        if (CollectionUtils.isNotEmpty(mrpContextDTO.getInventoryShiftDetailList())) {
            mrpResultPO.setDeleteInventoryShiftDetailIds(mrpContextDTO.getInventoryShiftDetailList().stream()
                    .map(GlassInventoryShiftDetailVO::getId).collect(Collectors.toList()));
            mrpResultPO.setDeleteInventoryOccupyIds(mrpContextDTO.getMaterialPlanInventoryOccupyList().stream()
                    .map(MaterialPlanInventoryOccupyVO::getId).collect(Collectors.toList()));
        }
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
        return mrpResultPO;
    }

    private void satisfyStandardDemandToBc(MrpContextDTO mrpContextDTO, List<GlassInventoryShiftDataPO> glassInventoryShiftDataCreateList,
                                       Map<String, BigDecimal> factoryOpeningInventory, Map<String, MaterialDayTotalDemandDTO> demandByProductAndDate,
                                       List<MaterialPlanInventoryOccupyPO> portOccupyList, List<GlassInventoryShiftDetailPO> inventoryShiftCreateList) {
        List<Date> inventoryShiftDateList = getInventoryShiftDateList(mrpContextDTO.getMrpCalcDate(), 12);
        Map<String, List<GlassSubstitutionRelationshipVO>> glassSubstitutionRelationshipMapOfProductionCode = mrpContextDTO.getGlassSubstitutionRelationshipMapOfProductionCode();
        for (GlassInventoryShiftDataPO inventoryShiftData : glassInventoryShiftDataCreateList) {
            String productCode = inventoryShiftData.getProductCode();
            List<String> mixSubstitutionList;
            if (mrpContextDTO.getMixSubstitution().containsKey(productCode)){
                mixSubstitutionList = mrpContextDTO.getMixSubstitution().get(productCode);
            }else {
                mixSubstitutionList = new ArrayList<>();
                mixSubstitutionList.add(productCode);
            }
            // 获取ERP-BOM(主料)
//            String mainProductCode = getMainProductCode(productCode, glassSubstitutionRelationshipVOList);
            for (Date inventoryShiftDate : inventoryShiftDateList) {
                // 创建推移明细数据
                GlassInventoryShiftDetailPO inventoryShiftDetailPO = new GlassInventoryShiftDetailPO();
                inventoryShiftDetailPO.setId(UUIDUtil.getUUID());
                inventoryShiftDetailPO.setInventoryShiftDataId(inventoryShiftData.getId());
                inventoryShiftDetailPO.setInventoryDate(inventoryShiftDate);
                inventoryShiftCreateList.add(inventoryShiftDetailPO);

                // 期初库存
                BigDecimal factoryInventoryQuantity = factoryOpeningInventory.get(productCode) == null ? BigDecimal.ZERO : factoryOpeningInventory.get(productCode);
                inventoryShiftDetailPO.setOpeningInventory(factoryInventoryQuantity);

                BigDecimal satisfyStandardDemand = BigDecimal.ZERO;
                // 获取当前日期的标准规格需求
                // 总需求
                String demandKey = String.join("&&", productCode, DateUtils.dateToString(inventoryShiftDate));
                MaterialDayTotalDemandDTO materialDaysDemandDO = demandByProductAndDate.get(demandKey);
                // 标准规格需求
                BigDecimal standardDemand = materialDaysDemandDO == null ? BigDecimal.ZERO : materialDaysDemandDO.getStandardDemand();
                if (null == materialDaysDemandDO || standardDemand.compareTo(BigDecimal.ZERO) <= 0){
                    continue;
                }

                // 使用本厂库存满足标准规格需求
                if (standardDemand.compareTo(factoryInventoryQuantity) <= 0){
                    // 本厂库存能满足标准规格库存
                    satisfyStandardDemand = standardDemand;
                    // 扣减本厂库存
                    factoryInventoryQuantity = factoryInventoryQuantity.subtract(standardDemand);
                    factoryOpeningInventory.put(productCode, factoryInventoryQuantity);
                }else if (standardDemand.compareTo(factoryInventoryQuantity) > 0){
                    // 本厂库存不能满足标准规格库存
                    satisfyStandardDemand = factoryInventoryQuantity;
                    standardDemand = standardDemand.subtract(factoryInventoryQuantity);
                    // 扣减本厂库存
                    factoryOpeningInventory.put(productCode, BigDecimal.ZERO);
                }
                if (satisfyStandardDemand.compareTo(standardDemand) == 0){
                    materialDaysDemandDO.setSatisfyStandardDemand(satisfyStandardDemand);
                    continue;
                }

                // 使用码头库存满足标准规格需求
                BigDecimal unFulfillmentQuantity = allocateInventory(mrpContextDTO, productCode, inventoryShiftDetailPO, portOccupyList, standardDemand, inventoryShiftDate,
                        MrpSupplySourceEnum.PORT_INVENTORY.getCode(),
                        true, false, null, null);
                satisfyStandardDemand = satisfyStandardDemand.add(standardDemand.subtract(unFulfillmentQuantity));
                materialDaysDemandDO.setSatisfyStandardDemand(satisfyStandardDemand);

                // 码头整箱调拨
                if (CollectionUtils.isNotEmpty(portOccupyList)) {
                    Set<String> containerNumber = portOccupyList.stream()
                            .filter(t -> mixSubstitutionList.contains(t.getSupplyProductCode())
                                    && t.getTransportDateEnd().compareTo(inventoryShiftDate) == 0)
                            .map(MaterialPlanInventoryOccupyPO::getContainerNumber).collect(Collectors.toSet());
                    mtAllContainerTransfer(productCode, mrpContextDTO, inventoryShiftDate, inventoryShiftDetailPO,
                            portOccupyList, mrpContextDTO.getPortSupplyList(), containerNumber);
                }


            }
        }
    }

    private List<GlassInventoryShiftDataPO> sortInventoryShiftData(List<GlassInventoryShiftDataPO> glassInventoryShiftDataCreateList, MrpContextDTO mrpContextDTO) {
        // 获取原片替代映射
        List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipList = mrpContextDTO.getGlassSubstitutionRelationshipList();

        if (CollectionUtils.isEmpty(glassSubstitutionRelationshipList)) return glassInventoryShiftDataCreateList;

        // 主料集合和替代料集合
        Set<String> primaryMaterials = new HashSet<>();
        Set<String> alternativeMaterials = new HashSet<>();

        for (GlassSubstitutionRelationshipVO relation : glassSubstitutionRelationshipList) {
            // 主料
            primaryMaterials.add(relation.getRawProductCode());
            // 替代料
            alternativeMaterials.add(relation.getSubstituteProductCode());
        }

        // 进行排序
        glassInventoryShiftDataCreateList.sort((po1, po2) -> {
            String productCode1 = po1.getProductCode();
            String productCode2 = po2.getProductCode();

            // 确定物料排序级别
            int type1 = getProductSortingLevel(productCode1, primaryMaterials, alternativeMaterials);
            int type2 = getProductSortingLevel(productCode2, primaryMaterials, alternativeMaterials);

            // 根据级别排序
            return Integer.compare(type1, type2);
        });

        return glassInventoryShiftDataCreateList;
    }

    /**
     * 确定物料排序级别
     *
     * @param productCode          原片
     * @param primaryMaterials     主料
     * @param alternativeMaterials 替代料
     * @return 排序级别
     */
    private int  getProductSortingLevel(String productCode, Set<String> primaryMaterials, Set<String> alternativeMaterials) {
        boolean isPrimary = primaryMaterials.contains(productCode);
        boolean isAlternative = alternativeMaterials.contains(productCode);

        if (isPrimary && !isAlternative) {
            // 只作为主料
            return 1;
        } else if (isPrimary) {
            // 既是主料又是替代料
            return 2;
        } else if (isAlternative) {
            // 只作为替代料
            return 3;
        } else {
            // 无替代关系
            return 4;
        }
    }

    private static List<GlassInventoryShiftDataPO> createGlassInventoryShiftDataList(MrpContextDTO mrpContextDTO,
                                                                                     Set<String> productCodes,
                                                                                     MrpResultPO mrpResultPO) {
        List<GlassInventoryShiftDataPO> glassInventoryShiftDataList = new ArrayList<>(1024);

        for (String productCode : productCodes) {
            GlassInventoryShiftDataPO inventoryShiftData = new GlassInventoryShiftDataPO();
            if (mrpContextDTO.getAdjustInventoryShiftData() != null) {
                return Lists.newArrayList(GlassInventoryShiftDataConvertor.INSTANCE
                        .dto2Po(mrpContextDTO.getAdjustInventoryShiftData()));
            }
            GlassSafetyInventoryVO glassSafetyInventory = getGlassSafetyInventory(mrpContextDTO, productCode, mrpResultPO);
            inventoryShiftData.setId(UUIDUtil.getUUID());
            inventoryShiftData.setProductCode(productCode);
            inventoryShiftData.setSafetyStockDaysMin(glassSafetyInventory.getMinSafetyInventoryDays());
            inventoryShiftData.setSafetyStockDaysStandard(glassSafetyInventory.getStandardSafetyInventoryDays());
            inventoryShiftData.setSafetyStockDaysMax(glassSafetyInventory.getMaxSafetyInventoryDays());
            inventoryShiftData.setPortInventoryDays(glassSafetyInventory.getPortInventoryDays());
            Map<String, List<String>> mixSubstitution = mrpContextDTO.getMixSubstitution();
            List<String> mixProductCodes = mixSubstitution.get(productCode);
            StringBuilder productFactoryCode = new StringBuilder();
            StringBuilder vehicleModelCode = new StringBuilder();
            Map<String, List<NewProductStockPointVO>> glassOfFactoryCodeMap = mrpContextDTO.getGlassOfFactoryCodeMap();

            if (CollectionUtils.isNotEmpty(mixProductCodes)){
                for (String mixProductCode : mixProductCodes) {
                    List<NewProductStockPointVO> newProductStockPointVOS = glassOfFactoryCodeMap.get(mixProductCode);
                    if (newProductStockPointVOS != null) {
                        for (NewProductStockPointVO newProductStockPointVO : newProductStockPointVOS) {
                            if (StringUtils.isNotEmpty(newProductStockPointVO.getProductFactoryCode())
                                    && productFactoryCode.indexOf(newProductStockPointVO.getProductFactoryCode()) == -1) {
                                productFactoryCode.append(newProductStockPointVO.getProductFactoryCode()).append(",");
                            }
                            if (StringUtils.isNotEmpty(newProductStockPointVO.getVehicleModelCode()) &&
                                    vehicleModelCode.indexOf(newProductStockPointVO.getVehicleModelCode()) == -1) {
                                vehicleModelCode.append(newProductStockPointVO.getVehicleModelCode()).append(",");
                            }
                        }
                    }
                }
            }

            if (StringUtils.isNotEmpty(productFactoryCode)) {
                inventoryShiftData.setProductFactoryCode(productFactoryCode.substring(0, productFactoryCode.length() - 1));
            }
            if (StringUtils.isNotEmpty(vehicleModelCode)) {
                inventoryShiftData.setVehicleModeCode(vehicleModelCode.substring(0, vehicleModelCode.length() - 1));
            }
            glassInventoryShiftDataList.add(inventoryShiftData);
        }
        return glassInventoryShiftDataList;
    }

    private void createFloatInventoryShift(MrpContextDTO mrpContextDTO,
                                           List<GlassInventoryShiftDataPO> glassInventoryShiftDataCreateList,
                                           List<MaterialPlanInventoryOccupyPO> floatOccupyList,
                                           Map<String, BigDecimal> floatOpeningInventory,
                                           List<GlassInventoryShiftDetailPO> materialPlanInventoryShiftCreateList) {
        List<MaterialPlanTransferVO> floatTransferList = new ArrayList<>();
        // 根据物料分组
        Set<String> floatCodes = new HashSet<>();
        if (CollectionUtils.isNotEmpty(mrpContextDTO.getMaterialPlanTransferList())) {
            floatTransferList = mrpContextDTO.getMaterialPlanTransferList().stream()
                    .filter(t -> mrpContextDTO.getFloatStockPointCodeList().contains(t.getStockPointCodeFrom()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(floatTransferList)) {
                floatTransferList.forEach(t -> floatCodes.add(t.getStockPointCodeFrom()));
            }
        }
        if (CollectionUtils.isNotEmpty(floatOccupyList)) {
            floatOccupyList.forEach(t -> floatCodes.add(t.getStockPointCodeFrom()));
        }

        // 库存推移日期范围
        List<Date> inventoryShiftDateList = getInventoryShiftDateList(mrpContextDTO.getMrpCalcDate(), 60);
        for (GlassInventoryShiftDataPO inventoryShiftData : glassInventoryShiftDataCreateList) {
            String productCode = inventoryShiftData.getProductCode();
            List<String> mixSubstitutionList;
            if (mrpContextDTO.getMixSubstitution().containsKey(productCode)){
                mixSubstitutionList = mrpContextDTO.getMixSubstitution().get(productCode);
            }else {
                mixSubstitutionList = new ArrayList<>();
                mixSubstitutionList.add(productCode);
            }
            for (String floatCode : floatCodes) {
                for (Date inventoryShiftDate : inventoryShiftDateList) {
                    GlassInventoryShiftDetailPO ffInventoryShift = new GlassInventoryShiftDetailPO();
                    ffInventoryShift.setInventoryShiftDataId(inventoryShiftData.getId());
                    ffInventoryShift.setInventoryDate(inventoryShiftDate);
                    ffInventoryShift.setStockPointCode(floatCode);
                    ffInventoryShift.setStockPointType(MrpStockPointTypeEnum.FF.getCode());
                    // 期初库存
                    BigDecimal openingInventory = floatOpeningInventory.get(productCode + "&&" + floatCode);
                    openingInventory = openingInventory == null ? BigDecimal.ZERO : openingInventory;

                    if (openingInventory.compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }

                    ffInventoryShift.setOpeningInventory(openingInventory);
                    // 已发布调拨-运入本厂
                    BigDecimal outputQuantityToBc = floatTransferList.stream()
                            .filter(t -> t.getStockPointCodeFrom().equals(floatCode)
                                    && mrpContextDTO.getBcStockPointCodeList().contains(t.getStockPointCodeTo())
                                    && mixSubstitutionList.contains(t.getProductCode())
                                    && t.getTransferDateDepart().compareTo(inventoryShiftDate) == 0)
                            .map(MaterialPlanTransferVO::getTransferQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    ffInventoryShift.setOutputQuantityToBc(outputQuantityToBc);
                    // 已发布调拨-运入码头
                    BigDecimal outputQuantityToPort = floatTransferList.stream()
                            .filter(t -> t.getStockPointCodeFrom().equals(floatCode)
                                    && mrpContextDTO.getPortStockPointCodeList().contains(t.getStockPointCodeTo())
                                    && mixSubstitutionList.contains(t.getProductCode())
                                    && t.getTransferDateDepart().compareTo(inventoryShiftDate) == 0)
                            .map(MaterialPlanTransferVO::getTransferQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    ffInventoryShift.setOutputQuantityToPort(outputQuantityToPort);

                    // 决策发出量（汽运）
                    BigDecimal decisionOutputToBc = floatOccupyList.stream()
                            .filter(t -> t.getStockPointCodeFrom().equals(floatCode)
                                    && mrpContextDTO.getBcStockPointCodeList().contains(t.getStockPointCodeTo())
                                    && DateUtils.getDayFirstTime(t.getOccupyDate()).compareTo(inventoryShiftDate) == 0
                                    && mixSubstitutionList.contains(t.getSupplyProductCode()))

                            .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    ffInventoryShift.setDecisionOutputQuantityToBc(decisionOutputToBc);
                    // 决策发出量（海运）
                    BigDecimal decisionOutputToPort = floatOccupyList.stream()
                            .filter(t -> t.getStockPointCodeFrom().equals(floatCode)
                                    && mrpContextDTO.getPortStockPointCodeList().contains(t.getStockPointCodeTo())
                                    && DateUtils.getDayFirstTime(t.getOccupyDate()).compareTo(inventoryShiftDate) == 0
                                    && mixSubstitutionList.contains(t.getSupplyProductCode()))
                            .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    ffInventoryShift.setDecisionOutputQuantityToPort(decisionOutputToPort);

                    // 期末库存 = 期初库存 - 已发布调拨-运入本厂 - 已发布调拨-运入码头 - 决策发出量（汽运）- 决策发出量（海运）
                    BigDecimal endingInventory = openingInventory.subtract(outputQuantityToBc).subtract(outputQuantityToPort)
                            .subtract(decisionOutputToBc).subtract(decisionOutputToPort);
                    ffInventoryShift.setEndingInventory(endingInventory);
                    materialPlanInventoryShiftCreateList.add(ffInventoryShift);
                    floatOpeningInventory.put(productCode + "&&" + floatCode, endingInventory);
                }
            }
        }
    }

    private void createPortInventoryShift(MrpContextDTO mrpContextDTO,
                                          List<GlassInventoryShiftDataPO> glassInventoryShiftDataCreateList,
                                          List<MaterialPlanInventoryOccupyPO> floatOccupyList,
                                          List<MaterialPlanInventoryOccupyPO> portOccupyList,
                                          Map<String, BigDecimal> portOpeningInventory,
                                          List<GlassInventoryShiftDetailPO> mtInventoryShiftCreateList) {
        String portStockPointCode = String.join("&&", mrpContextDTO.getPortStockPointCodeList());
        Map<String, BigDecimal> portOpeningInventoryHasAdjust = new HashMap<>(portOpeningInventory);
        Map<String, List<GlassSubstitutionRelationshipVO>> glassSubstitutionRelationshipMapOfProductionCode = mrpContextDTO.getGlassSubstitutionRelationshipMapOfProductionCode();

        // 库存推移日期范围
        List<Date> inventoryShiftDateList = getInventoryShiftDateList(mrpContextDTO.getMrpCalcDate(), 60);
        for (GlassInventoryShiftDataPO glassInventoryShiftData : glassInventoryShiftDataCreateList) {
            String productCode = glassInventoryShiftData.getProductCode();
            List<String> mixSubstitutionList;
            if (mrpContextDTO.getMixSubstitution().containsKey(productCode)){
                mixSubstitutionList = mrpContextDTO.getMixSubstitution().get(productCode);
            }else {
                mixSubstitutionList = new ArrayList<>();
                mixSubstitutionList.add(productCode);
            }

            int index = 0;
            for (Date inventoryShiftDate : inventoryShiftDateList) {
                GlassInventoryShiftDetailPO mtInventoryShift = new GlassInventoryShiftDetailPO();
                // 决策运出量
                BigDecimal decisionOutputQuantityToBc = portOccupyList.stream()
                        .filter(t -> portStockPointCode.contains(t.getStockPointCodeFrom())
                                && mixSubstitutionList.contains(t.getSupplyProductCode())
                                && DateUtils.getDayFirstTime(t.getOccupyDate()).compareTo(inventoryShiftDate) == 0)
                        .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                mtInventoryShift.setDecisionOutputQuantityToBc(decisionOutputQuantityToBc);

                // 已发布调拨-运入本厂
                BigDecimal outputQuantityToBc = mrpContextDTO.getMaterialPlanTransferList().stream()
                        .filter(t -> portStockPointCode.contains(t.getStockPointCodeFrom())
                                && mixSubstitutionList.contains(t.getProductCode())
                                && DateUtils.getDayFirstTime(t.getTransferDateDepart()).compareTo(inventoryShiftDate) == 0)
                        .map(MaterialPlanTransferVO::getTransferQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                mtInventoryShift.setOutputQuantityToBc(outputQuantityToBc);


                mtInventoryShift.setInventoryShiftDataId(glassInventoryShiftData.getId());
                mtInventoryShift.setStockPointCode(portStockPointCode);
                mtInventoryShift.setStockPointType(MrpStockPointTypeEnum.MT.getCode());
                mtInventoryShift.setInventoryDate(inventoryShiftDate);
                // 期初库存
                // 期初库存-码头：第一天，取调拨前的库存（也可以理解为已发布码头送柜+现在码头库存）。后面取前一天的期末库存
//                BigDecimal openingInventory = portOpeningInventory.get(productCode);
                BigDecimal openingInventory = portOpeningInventoryHasAdjust.get(productCode);
                openingInventory = openingInventory == null ? BigDecimal.ZERO : openingInventory;
                if (index == 0) {
                    openingInventory = openingInventory.add(outputQuantityToBc);
                }
                mtInventoryShift.setOpeningInventory(openingInventory);

                // 已发布调拨-运入码头
                BigDecimal inputQuantity = mrpContextDTO.getMaterialPlanTransferList().stream()
                        .filter(t -> portStockPointCode.contains(t.getStockPointCodeTo())
                                && mixSubstitutionList.contains(t.getProductCode())
                                && DateUtils.getDayFirstTime(t.getTransferDateArrive()).compareTo(inventoryShiftDate) == 0)
                        .map(MaterialPlanTransferVO::getTransferQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                mtInventoryShift.setInputQuantity(inputQuantity);
                // 码头在途
                BigDecimal transitQuantityFromFloat = mrpContextDTO.getPortSupplyList().stream()
                        .filter(t -> portStockPointCode.contains(t.getStockPointCode())
                                && mixSubstitutionList.contains(t.getProductCode())
                                && t.getSupplySource().equals(MrpSupplySourceEnum.PORT_ON_WAY.getCode())
                                && inventoryShiftDate.compareTo(DateUtils.getDayFirstTime(t.getSupplyTime())) == 0)
                        .map(MrpSupplyDTO::getSupplyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                mtInventoryShift.setTransitQuantityFromPort(transitQuantityFromFloat);

                // 计划调拨前期末库存 = 期初库存 + 码头在途 + 计划到柜量 - 计划出柜量
                BigDecimal endingInventoryBeforeAdjust = openingInventory.add(transitQuantityFromFloat).add(inputQuantity)
                        .subtract(outputQuantityToBc);
                portOpeningInventory.put(productCode, endingInventoryBeforeAdjust);


                // 计划调整量
                BigDecimal adjustQuantityFromFloat = floatOccupyList.stream()
                        .filter(t -> portStockPointCode.contains(t.getStockPointCodeTo())
                                && mixSubstitutionList.contains(t.getSupplyProductCode())
                                && DateUtils.getDayFirstTime(t.getTransportDateEnd()).compareTo(inventoryShiftDate) == 0)
                        .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                mtInventoryShift.setAdjustQuantityFromFloat(adjustQuantityFromFloat);

                // 期末库存 = 期初库存 + 码头在途 + 计划到柜量 + 调整量 - 计划出柜量 - 决策运出量
                // 期末库存 = 期初库存 + 码头在途 + 计划到柜量 + 调整量 - 计划调拨码头送柜 - 已发布码头送柜
                // 现有期初逻辑
                BigDecimal openingInventoryHasAdjust = portOpeningInventoryHasAdjust.get(productCode) == null
                        ? BigDecimal.ZERO : portOpeningInventoryHasAdjust.get(productCode);
                BigDecimal endingInventoryHasAdjust = openingInventory.add(transitQuantityFromFloat).add(inputQuantity)
                        .add(adjustQuantityFromFloat).subtract(outputQuantityToBc).subtract(decisionOutputQuantityToBc);
                mtInventoryShift.setEndingInventory(endingInventoryHasAdjust);
                portOpeningInventoryHasAdjust.put(productCode, endingInventoryHasAdjust);
                mtInventoryShiftCreateList.add(mtInventoryShift);
                index++;
            }
        }
    }

    private static void assemblyMaterialReplace(MrpContextDTO mrpContextDTO,
                                                List<GlassInventoryShiftDataPO> glassInventoryShiftDataCreateList,
                                                List<GlassInventoryShiftDetailPO> glassInventoryShiftDetailCreateList,
                                                List<MaterialPlanReplacePO> materialPlanReplaceCreateList) {

        Map<String, String> glassInventoryShiftDataMap = glassInventoryShiftDataCreateList.stream()
                .collect(Collectors.toMap(GlassInventoryShiftDataPO::getId, GlassInventoryShiftDataPO::getProductCode));

        Map<String, NewStockPointVO> stockPointCodeMap = mrpContextDTO.getStockPointVOList().stream()
                .collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity(), (o1, o2) -> o1));

        List<GlassInventoryShiftDetailPO> useAsReplaceInventoryShift = glassInventoryShiftDetailCreateList.stream()
                .filter(data -> null != data.getUsedAsReplaceQuantity())
                .filter(t -> {
                    Set<String> stockPointType = new HashSet<>();
                    Arrays.asList(t.getStockPointCode().split("&&"))
                            .forEach(stockPointCode -> stockPointType.add(stockPointCodeMap.get(stockPointCode).getStockPointType()));
                    return stockPointType.size() == 1
                            && stockPointType.contains(StockPointTypeEnum.BC.getCode());
                })
                .filter(t -> t.getUsedAsReplaceQuantity().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(useAsReplaceInventoryShift)) return;
        // 根据物料编码和日期分组
        Map<String, GlassInventoryShiftDetailPO> useReplaceInventoryShiftMap = useAsReplaceInventoryShift.stream()
                .collect(Collectors.toMap(t -> glassInventoryShiftDataMap.get(t.getInventoryShiftDataId())
                                + "&&" + DateUtils.dateToString(t.getInventoryDate()),
                        Function.identity()));

        for (MaterialDayTotalDemandDTO materialDayTotalDemandDTO : mrpContextDTO.getMaterialDayTotalDemandList()) {

            if (materialDayTotalDemandDTO.getUseReplaceQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            materialDayTotalDemandDTO.getUseReplaceDetailList().forEach(t -> {
                String substituteProductShiftKey = t.getProductCode() + "&&"
                        + DateUtils.dateToString(materialDayTotalDemandDTO.getDemandDate());
                if (!useReplaceInventoryShiftMap.containsKey(substituteProductShiftKey))
                    return;
                // 替代料的期初库存
                BigDecimal openingInventory = useReplaceInventoryShiftMap.get(substituteProductShiftKey).getOpeningInventory();
                // 计划替代数量
                // 实际替代数量
                BigDecimal actualUseReplaceQuantity = openingInventory.compareTo(t.getReplaceQuantity()) <= 0
                        ? openingInventory : t.getReplaceQuantity();
                if (actualUseReplaceQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                    return;
                }
                MaterialPlanReplacePO materialPlanReplacePO = new MaterialPlanReplacePO();
                materialPlanReplacePO.setReplaceDate(materialDayTotalDemandDTO.getDemandDate());
                materialPlanReplacePO.setReplaceType(MrpReplaceTypeEnum.PLAN_REPLACE.getCode());
                materialPlanReplacePO.setMasterProductCode(materialDayTotalDemandDTO.getProductCode());
                materialPlanReplacePO.setReplaceProductCode(t.getProductCode());
                materialPlanReplacePO.setReplaceQuantity(actualUseReplaceQuantity);
                materialPlanReplacePO.setRemark(t.getRemark());
                materialPlanReplacePO.setInventoryAlternativeRelationshipId(t.getInventoryAlternativeRelationshipId());
                materialPlanReplaceCreateList.add(materialPlanReplacePO);
            });

        }
    }

    @Override
    protected List<MaterialArrivalTrackingDTO> doDateRecommend(MrpContextDTO mrpContextDTO) {
        return Collections.emptyList();
    }

}
