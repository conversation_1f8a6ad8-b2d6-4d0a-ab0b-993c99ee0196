package com.yhl.scp.mrp.config;

import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

/**
 * @ClassName CommandLineRunnerImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/01/07 14:07
 * @Version 1.0
 */
@Component
@Order(value = 1)
@Slf4j
public class CommandLineRunnerImpl implements CommandLineRunner {

    @Resource
    private RedisUtil redisUtil;

    @Override
    public void run(String... args) {
        log.info("重启删除未释放的分布式锁");
        String mrpNoGlassKey = RedisKeyManageEnum.MRP_COMPUTE.getKey();
        Set<String> syncKeys = redisUtil.keys(mrpNoGlassKey + "*");
        syncKeys.add(RedisKeyManageEnum.NO_GLASS_MRP_PUBLISHED_KEY.getKey());
        for (String key : syncKeys) {
            redisUtil.delete(key);
        }
    }

}
