package com.yhl.scp.ips.datapermission.config;

import com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration;
import com.yhl.scp.ips.config.MybatisChangeRecordInterceptor;
import com.yhl.scp.ips.datapermission.interceptor.DataPermissionInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <code>DataPermissionConfig</code>
 * <p>
 * 数据权限配置
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-10 17:01:33
 */
@Configuration
// @ConditionalOnProperty(value = "datapermission.enabled", havingValue = "true", matchIfMissing = true)
@AutoConfigureAfter(PageHelperAutoConfiguration.class)
@Slf4j
@Component
public class DataPermissionConfig {

    @Autowired
    PageHelperAutoConfiguration pageHelperAutoConfiguration;
    @Autowired
    private List<SqlSessionFactory> sqlSessionFactoryList;
    @Autowired
    private DataPermissionAnalysis dataPermissionAnalysis;

    // 执行分页服务启动执行
    @PostConstruct
    public void init() {
        // 添加SQL拦截器，在执行分页bean后调用拦截器
        DataPermissionInterceptor dataPermissionInterceptor = new DataPermissionInterceptor(dataPermissionAnalysis);
        for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
            sqlSessionFactory.getConfiguration().addInterceptor(dataPermissionInterceptor);
            sqlSessionFactory.getConfiguration().addInterceptor(new MybatisChangeRecordInterceptor());
        }
    }

    // @Value("${spring.datasource.url}")
    // private String url;
    //
    // @Value("${spring.datasource.username}")
    // private String username;
    //
    // @Value("${spring.datasource.password}")
    // private String password;
    //
    // @Value("${spring.datasource.driverClassName}")
    // private String driver;

    // @Autowired
    // private List<SqlSessionFactory> sqlSessionFactoryList;
    //
    // /**
    //  * 在分页拦截器后加载
    //  */
    // @Autowired
    // PageHelperAutoConfiguration pageHelperAutoConfiguration;

    // // 数据权限规则
    // private static Map<String, DataPermissionRuleVO> rule = new HashMap<>();
    //
    // // 数据权限映射关系
    // private static Map<String, DataPermissionMapperVO> mapper = new HashMap<>();

    // 查询数据权限规则
    // private static final String QUERY_RULE = "SELECT id,rule_name,auth_sql FROM auth_rbac_data_permission_rule";
    //
    // // 查询数据权限映射关系
    // private static final String QUERY_MAPPER = "SELECT id,mapper_code,data_permission_rule_id FROM auth_rbac_data_permission_mapper";
    //
    // // 变量替换的正则表达式
    // private static final Pattern REGEX = Pattern.compile("[$][{]\\w+[}]");


    // @PostConstruct
    // public void init() {
    //     // 刷新数据权限配置
    //     // refreshConfig();
    //     DataPermissionInterceptor dataPermissionInterceptor = new DataPermissionInterceptor();
    //     for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
    //         sqlSessionFactory.getConfiguration().addInterceptor(dataPermissionInterceptor);
    //     }
    // }

    // /**
    //  * 通过权限配置获取权限sql
    //  *
    //  * @return
    //  */
    // public static String getAuthSql() {
    //     if (DataPermissionDefinitionHolder.getParams() == null) {
    //         return null;
    //     }
    //     // 若找不到配置，则直接返回
    //     DataPermissionMapperVO dataPermissionMapperVO = mapper.get(DataPermissionDefinitionHolder.getParams());
    //     if (dataPermissionMapperVO == null) {
    //         return null;
    //     }
    //     DataPermissionRuleVO dataPermissionRuleVO = dataPermissionMapperVO.getDataPermissionRuleVO();
    //     String authSql = dataPermissionRuleVO.getAuthSql();
    //     // 查询全部数据
    //     if (StringUtils.isBlank(authSql)) {
    //         return null;
    //     }
    //     User user = SystemHolder.getUser();
    //     // 变量替换
    //     Matcher matcher = REGEX.matcher(authSql);
    //     while (matcher.find()) {
    //         String str = matcher.group();
    //         String key = str.substring(2, str.length() - 1);
    //         String value = "'" + getValue(user, key) + "'";
    //         authSql = authSql.replaceAll("[$][{]" + key + "[}]", value);
    //     }
    //     return authSql;
    // }
    //
    // /**
    //  * 获取用户信息
    //  *
    //  * @param user
    //  * @param key
    //  * @return java.lang.String
    //  */
    // private static String getValue(User user, String key) {
    //     try {
    //         Class<?> userClass = user.getClass();
    //         Field field = userClass.getDeclaredField(key);
    //         field.setAccessible(true);
    //         return (String) field.get(user);
    //     } catch (NoSuchFieldException | IllegalAccessException e) {
    //         throw new RuntimeException(e);
    //     }
    // }
    //
    // /**
    //  * 拼接in条件
    //  *
    //  * @param elements
    //  * @return java.lang.String
    //  */
    // private static String buildInCondition(Set<String> elements) {
    //     StringBuilder sb = new StringBuilder();
    //     boolean first = true;
    //     for (String element : elements) {
    //         if (!first) {
    //             sb.append(", ");
    //         }
    //         sb.append('\'').append(element).append('\'');
    //         first = false;
    //     }
    //     return sb.toString();
    // }
    //
    // /**
    //  * 刷新数据权限配置
    //  */
    // public void refreshConfig() {
    //     // 刷新规则
    //     refreshRule();
    //     // 刷新映射关系
    //     refreshMapper();
    // }
    //
    // /**
    //  * 刷新规则
    //  */
    // private void refreshRule() {
    //     Properties properties = new Properties();
    //     DataSource dataSource = new DriverDataSource(url, driver, properties, username, password);
    //     Map<String, DataPermissionRuleVO> newRule = new HashMap<>();
    //     try (Connection connection = dataSource.getConnection();
    //          PreparedStatement preparedStatement = connection.prepareStatement(QUERY_RULE);
    //          ResultSet resultSet = preparedStatement.executeQuery()) {
    //         while (resultSet.next()) {
    //             DataPermissionRuleVO dataPermissionRuleVO = new DataPermissionRuleVO();
    //             dataPermissionRuleVO.setId(resultSet.getString(1));
    //             dataPermissionRuleVO.setRuleName(resultSet.getString(2));
    //             dataPermissionRuleVO.setAuthSql(resultSet.getString(3));
    //             newRule.put(dataPermissionRuleVO.getId(), dataPermissionRuleVO);
    //         }
    //     } catch (SQLException e) {
    //         log.error("DataPermissionConfig.refreshRule", e);
    //     }
    //     rule = newRule;
    // }
    //
    // /**
    //  * 刷新映射关系
    //  */
    // private void refreshMapper() {
    //     Properties properties = new Properties();
    //     DataSource dataSource = new DriverDataSource(url, driver, properties, username, password);
    //     Map<String, DataPermissionMapperVO> newMapper = new HashMap<>();
    //     try (Connection connection = dataSource.getConnection();
    //          PreparedStatement preparedStatement = connection.prepareStatement(QUERY_MAPPER);
    //          ResultSet resultSet = preparedStatement.executeQuery()) {
    //         while (resultSet.next()) {
    //             DataPermissionMapperVO dataPermissionMapperVO = new DataPermissionMapperVO();
    //             dataPermissionMapperVO.setId(resultSet.getString(1));
    //             dataPermissionMapperVO.setMapperCode(resultSet.getString(2));
    //             String ruleId = resultSet.getString(3);
    //             DataPermissionRuleVO dataPermissionRuleVO = rule.get(ruleId);
    //             if (dataPermissionRuleVO != null) {
    //                 dataPermissionMapperVO.setDataPermissionRuleVO(dataPermissionRuleVO);
    //                 newMapper.put(dataPermissionMapperVO.getMapperCode(), dataPermissionMapperVO);
    //             } else {
    //                 log.error("DataPermissionRuleVO is null: {}", ruleId);
    //             }
    //         }
    //     } catch (SQLException e) {
    //         log.error("DataPermissionConfig.refreshMapper", e);
    //     }
    //     mapper = newMapper;
    // }

}
