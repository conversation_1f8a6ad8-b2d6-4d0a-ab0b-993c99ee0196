package com.yhl.scp.ips.common;

import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;

public class ObjectUnitUtil {

    public static String getKey(String objectType, String fieldCode) {
        StringBuilder builder = new StringBuilder();
        builder.append(Constants.OBJ_UNIT_PREFIX)
                .append(objectType)
                .append(Constants.OBJ_UNIT_CONNECTOR)
                .append(fieldCode)
                .append(Constants.OBJ_UNIT_CONNECTOR)
                .append(SystemHolder.getTenantId());
        return builder.toString();
    }

    public static String getRedisKey(String key) {
        return String.format(RedisKeyManageEnum.YHL_OBJ_UNIT.getKey(), key);
    }

}
